# Developer Guide

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [.github/ISSUE_TEMPLATE/bug-report.md](.github/ISSUE_TEMPLATE/bug-report.md)
- [.github/ISSUE_TEMPLATE/feature-request.md](.github/ISSUE_TEMPLATE/feature-request.md)
- [.github/ISSUE_TEMPLATE/question.md](.github/ISSUE_TEMPLATE/question.md)
- [.github/PULL_REQUEST_TEMPLATE.md](.github/PULL_REQUEST_TEMPLATE.md)
- [.github/workflows/codeql-analysis.yml](.github/workflows/codeql-analysis.yml)
- [.github/workflows/test_suite.yml](.github/workflows/test_suite.yml)
- [.pre-commit-config.yaml](.pre-commit-config.yaml)
- [CONTRIBUTING.md](CONTRIBUTING.md)

</details>



## Purpose and Scope

This guide provides essential information for developers who want to contribute to the MoviePy project. It covers development environment setup, project structure, coding standards, testing procedures, and the contribution workflow. For user-oriented documentation on how to use MoviePy, see [Overview](#1) and [Getting Started](#1.1).

## Setting Up the Development Environment

### Forking and Cloning the Repository

1. Fork the official MoviePy repository to your own GitHub account using the "Fork" button
2. Clone your fork to your local machine:
   ```bash
   git clone https://github.com/YOUR-USERNAME/moviepy.git
   ```
3. Add the official repository as a remote named "upstream":
   ```bash
   git remote add upstream https://github.com/Zulko/moviepy.git
   ```

### Installing Dependencies

Install MoviePy in development mode with all dependencies:

```bash
pip install -e ".[optional,doc,test,lint]"
```

### Pre-commit Hooks Configuration

MoviePy uses pre-commit hooks to ensure code quality:

```bash
pre-commit install
```

This will configure the following tools to run automatically before each commit:
- Black (code formatter)
- isort (import sorter)
- flake8 (code linter)

Sources: [CONTRIBUTING.md:10-27](), [.pre-commit-config.yaml:1-26]()

## Project Structure Overview

### MoviePy Codebase Organization

```mermaid
graph TD
    Root["moviepy/"] --> MoviePyInit["__init__.py"]
    Root --> Version["version.py"]
    Root --> Decorators["decorators.py"]
    Root --> Effect["Effect.py"]
    
    Root --> VideoRoot["video/"]
    Root --> AudioRoot["audio/"]
    
    subgraph "Video Module"
        VideoRoot --> VideoClipPy["VideoClip.py"]
        VideoRoot --> IO["io/"]
        VideoRoot --> FX["fx/"]
        VideoRoot --> Compositing["compositing/"]
        VideoRoot --> Tools["tools/"]
    end
    
    subgraph "Audio Module"
        AudioRoot --> AudioClipPy["AudioClip.py"]
        AudioRoot --> AudioIO["io/"]
        AudioRoot --> AudioFX["fx/"]
        AudioRoot --> AudioTools["tools/"]
    end
    
    Root --> TestSuite["tests/"]
    
    IO --> FFmpegReader["ffmpeg_reader.py"]
    IO --> FFmpegWriter["ffmpeg_writer.py"]
    IO --> FFmpegTools["ffmpeg_tools.py"]
    
    FX --> EffectModules["Various effect modules (.py)"]
    
    TestSuite --> TestModules["Test modules (.py)"]
```

The MoviePy codebase is structured around a central `Clip` class with specialized implementations for video and audio content. The main modules are:

- **Core module**: Base classes and utilities
- **Video module**: VideoClip classes, effects, and compositing tools
- **Audio module**: AudioClip classes, effects, and processing tools
- **I/O module**: Readers and writers for media files using FFmpeg
- **Tests**: Test suite for all components

## Development Workflow

### Branching Strategy

```mermaid
gitGraph
   commit id: "upstream/master"
   branch "feature_branch"
   checkout "feature_branch"
   commit id: "implement feature"
   commit id: "add tests"
   checkout "master"
   merge "feature_branch" tag: "PR"
```

Follow these guidelines for branching:

1. Keep your local `master` branch in sync with upstream:
   ```bash
   git pull upstream master
   ```

2. Create a feature or bugfix branch for your changes:
   ```bash
   git checkout -b feature_your_feature_name
   # or
   git checkout -b fix_bug_description
   ```

3. Make your changes on this branch, not directly on `master`

Sources: [CONTRIBUTING.md:30-43]()

### Making Changes and Code Quality

- Follow PEP 8 coding conventions
- Write clear, self-documenting code with explicit variable names
- Add appropriate comments and documentation
- Document new functionality in docstrings
- Run pre-commit hooks to ensure code quality

### Pull Request Process

1. Run tests to verify your changes:
   ```bash
   pytest
   ```

2. Push your branch to your fork:
   ```bash
   git push origin your_branch_name
   ```

3. Create a pull request on GitHub, following the PR template
4. Address any review feedback

Sources: [CONTRIBUTING.md:46-59](), [.github/PULL_REQUEST_TEMPLATE.md:1-9]()

## Testing Framework

### Overview of MoviePy Testing Architecture

```mermaid
graph TD
    subgraph "Testing Components"
        PyTest["pytest"] --> TestModules["Test modules"]
        TestModules --> UnitTests["Unit tests"]
        TestModules --> DocTests["Doctests"]
        TestModules --> IntegrationTests["Integration tests"]
    end
    
    subgraph "CI Integration"
        GHActions["GitHub Actions"] --> TestSuite["Test suite"]
        TestSuite --> MacOSTests["macOS tests"]
        TestSuite --> WindowsTests["Windows tests"]
        TestSuite --> LinuxTests["Linux tests"]
    end
    
    TestModules --> Coverage["Coverage reporting"]
```

### Writing Tests

When adding new features or fixing bugs, include relevant tests:

- **Unit tests**: Test individual functions and classes
- **Integration tests**: Test interactions between components
- **Doctests**: Include examples in docstrings that can be tested

Tests should be placed in the `tests/` directory, following the structure of the main codebase.

### Running Tests

Run the complete test suite:
```bash
pytest
```

Run tests with coverage reporting:
```bash
pytest --cov moviepy --cov-report term-missing
```

Run doctests only:
```bash
pytest --doctest-glob "moviepy/**/**.py"
```

Sources: [.github/workflows/test_suite.yml:52-54](), [.github/workflows/test_suite.yml:79-82](), [.github/workflows/test_suite.yml:119-121]()

## Continuous Integration and Deployment

### GitHub Actions Workflows

MoviePy uses GitHub Actions for CI/CD:

```mermaid
graph TD
    Push["Push/PR"] --> TestSuite["Test Suite Workflow"]
    Push --> CodeQL["CodeQL Analysis"]
    
    subgraph "Test Suite Workflow"
        TestSuite --> MacOS["macOS Tests"]
        TestSuite --> Windows["Windows Tests"]
        TestSuite --> Linux["Linux Tests"]
        
        MacOS & Windows & Linux --> PythonVersions["Python 3.9, 3.10, 3.11"]
    end
    
    subgraph "CodeQL Analysis"
        CodeQL --> SecurityChecks["Security Checks"]
    end
```

The CI system automatically runs on:
- Every push to the master branch
- Every pull request
- Scheduled runs for CodeQL security analysis

#### Test Suite Workflow

The test suite runs on three operating systems:
- macOS (using miniconda environment)
- Windows
- Linux

Each platform tests with Python versions 3.9, 3.10, and 3.11.

The workflow:
1. Sets up the Python environment
2. Installs dependencies
3. Runs the test suite with pytest
4. Reports test coverage

#### CodeQL Security Analysis

CodeQL performs automated code scanning to identify potential security vulnerabilities and coding issues.

Sources: [.github/workflows/test_suite.yml:1-129](), [.github/workflows/codeql-analysis.yml:1-57]()

## Contributing Best Practices

### Issue Creation

When creating issues:
- For bugs: Use the bug report template and provide reproducible examples
- For features: Use the feature request template
- For questions: Consider using GitHub Discussions first

### Communication Guidelines

- Keep GitHub comments focused and on-topic
- Use [MoviePy Gitter](https://gitter.im/movie-py/Lobby) for longer discussions
- Do not push commits that change the API without prior discussion

### Documentation

When contributing code:
- Document new features in docstrings
- Update relevant documentation files
- Include examples when appropriate
- Ensure documentation follows the project style

Sources: [CONTRIBUTING.md:1-9](), [.github/ISSUE_TEMPLATE/bug-report.md:1-47](), [.github/ISSUE_TEMPLATE/feature-request.md:1-11](), [.github/ISSUE_TEMPLATE/question.md:1-22]()

## MoviePy Code Map

The following diagram shows the relationship between key code entities in MoviePy:

```mermaid
classDiagram
    class Clip {
        +duration
        +start
        +end
        +transform()
        +time_transform()
        +with_effects()
    }
    
    Clip <|-- VideoClip
    Clip <|-- AudioClip
    
    VideoClip <|-- ImageClip
    VideoClip <|-- ColorClip
    VideoClip <|-- TextClip
    VideoClip <|-- VideoFileClip
    VideoClip <|-- CompositeVideoClip
    
    AudioClip <|-- AudioFileClip
    AudioClip <|-- CompositeAudioClip
    
    class VideoClip {
        +frame_function
        +size
        +mask
        +audio
        +get_frame()
        +write_videofile()
    }
    
    class AudioClip {
        +frame_function
        +fps
        +get_frame()
        +to_soundarray()
        +write_audiofile()
    }
    
    VideoClip --> "0..1" AudioClip : has
    CompositeVideoClip --> "1..*" VideoClip : contains
    CompositeAudioClip --> "1..*" AudioClip : contains
```

This class hierarchy forms the foundation of the MoviePy library, with all media clips inheriting from the base `Clip` class and specializing into video or audio processing.

## Development Tools Integration

```mermaid
graph TD
    Developer["Developer"] --> LocalRepo["Local Repository"]
    
    subgraph "Local Development"
        LocalRepo --> PreCommit["Pre-commit Hooks"]
        PreCommit --> Black["Black Formatter"]
        PreCommit --> isort["Import Sorter"]
        PreCommit --> Flake8["Flake8 Linter"]
        
        LocalRepo --> PyTest["PyTest"]
    end
    
    LocalRepo --> GitHubFork["GitHub Fork"]
    GitHubFork --> PullRequest["Pull Request"]
    
    PullRequest --> GHActions["GitHub Actions"]
    GHActions --> TestSuite["Test Suite"]
    GHActions --> CodeQL["CodeQL Analysis"]
    
    PullRequest --> CodeReview["Code Review"]
    CodeReview --> Merge["Merge to Master"]
```

This diagram illustrates how the various development tools integrate into the MoviePy contribution workflow, from local development through to code review and merging.

Sources: [CONTRIBUTING.md:20-27](), [.pre-commit-config.yaml:1-26]()