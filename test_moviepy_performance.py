#!/usr/bin/env python3
"""
测试MoviePy 2.x升级后的性能改进
对比升级前后的视频生成性能
"""

import time
import sys
from pathlib import Path

def test_performance_comparison():
    """测试性能对比"""
    print("🚀 MoviePy 2.x性能测试")
    print("=" * 50)
    
    # 测试参数
    config_path = Path("精武英雄/lrc-mv.yaml")
    test_durations = [10.0, 20.0, 30.0]  # 不同时长的测试
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    from enhanced_generator import demo_enhanced_features
    
    results = []
    
    for duration in test_durations:
        print(f"\n🔍 测试 {duration} 秒视频生成...")
        
        start_time = time.perf_counter()
        try:
            success = demo_enhanced_features(
                config_path=config_path,
                t_max_sec=duration
            )
            end_time = time.perf_counter()
            
            if success:
                elapsed = end_time - start_time
                results.append((duration, elapsed))
                print(f"✅ {duration}秒视频生成成功，耗时: {elapsed:.2f}秒")
                print(f"   性能比率: {duration/elapsed:.2f}x (视频时长/生成时长)")
            else:
                print(f"❌ {duration}秒视频生成失败")
                
        except Exception as e:
            end_time = time.perf_counter()
            elapsed = end_time - start_time
            print(f"❌ {duration}秒视频生成异常: {e}")
            print(f"   失败前耗时: {elapsed:.2f}秒")
    
    # 性能分析
    if results:
        print("\n📊 性能分析结果:")
        print("-" * 30)
        total_video_time = sum(r[0] for r in results)
        total_generation_time = sum(r[1] for r in results)
        avg_ratio = total_video_time / total_generation_time
        
        print(f"总视频时长: {total_video_time:.1f}秒")
        print(f"总生成时长: {total_generation_time:.2f}秒")
        print(f"平均性能比率: {avg_ratio:.2f}x")
        
        if avg_ratio > 1.0:
            print("🎉 生成速度超过实时播放速度！")
        else:
            print("⚠️  生成速度低于实时播放速度")
            
        # 性能等级评估
        if avg_ratio >= 2.0:
            print("🏆 性能等级: 优秀 (2x+)")
        elif avg_ratio >= 1.0:
            print("🥈 性能等级: 良好 (1x+)")
        elif avg_ratio >= 0.5:
            print("🥉 性能等级: 一般 (0.5x+)")
        else:
            print("📉 性能等级: 需要优化 (<0.5x)")
            
        return True
    else:
        print("❌ 没有成功的测试结果")
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("\n🧠 内存使用测试...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始内存使用: {initial_memory:.1f} MB")
        
        # 执行一次视频生成
        from enhanced_generator import demo_enhanced_features
        config_path = Path("精武英雄/lrc-mv.yaml")
        
        demo_enhanced_features(config_path, t_max_sec=15.0)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"生成后内存使用: {final_memory:.1f} MB")
        print(f"内存增长: {memory_increase:.1f} MB")
        
        if memory_increase < 100:
            print("✅ 内存使用合理")
        elif memory_increase < 500:
            print("⚠️  内存使用较高")
        else:
            print("❌ 内存使用过高")
            
        return True
        
    except ImportError:
        print("⚠️  psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎬 MoviePy 2.x升级性能验证")
    print("=" * 60)
    
    # 检查MoviePy版本
    try:
        import moviepy
        print(f"📦 MoviePy版本: {moviepy.__version__}")
    except:
        print("❌ 无法获取MoviePy版本信息")
    
    # 运行性能测试
    perf_success = test_performance_comparison()
    
    # 运行内存测试
    memory_success = test_memory_usage()
    
    print("\n" + "=" * 60)
    if perf_success and memory_success:
        print("🎉 所有测试通过！MoviePy升级成功！")
        print("💡 建议: 可以开始使用新版本进行生产环境的视频生成")
        return True
    else:
        print("⚠️  部分测试未通过，建议进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
