# Compositing

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/video/compositing/CompositeVideoClip.py](moviepy/video/compositing/CompositeVideoClip.py)
- [moviepy/video/tools/drawing.py](moviepy/video/tools/drawing.py)

</details>



This document covers the compositing functionality in MoviePy, which allows for combining multiple video and audio clips into a unified composition. Compositing is a critical component of the media processing pipeline, sitting between the transformation and output stages. It enables users to create complex video layouts, sequences, and overlays.

## Overview of Compositing in MoviePy

Compositing in MoviePy involves combining multiple video clips either spatially (displaying them simultaneously in different positions) or temporally (playing them one after another). The library provides several key components for compositing:

```mermaid
flowchart TD
    subgraph "Compositing System"
        direction TB
        CVC["CompositeVideoClip"]
        CAC["CompositeAudioClip"]
        CF["Compositing Functions"]
        
        CF --> CA["clips_array()"]
        CF --> CCV["concatenate_videoclips()"]
    end
    
    subgraph "Inputs"
        VC["VideoClip instances"]
        AC["AudioClip instances"]
    end
    
    subgraph "Parameters"
        Pos["Position (pos)"]
        Layer["Layer (layer_index)"]
        Size["Size"]
        BG["Background Color"]
        Mask["Masks"]
    end
    
    VC --> CVC
    AC --> CAC
    Pos --> CVC
    Layer --> CVC
    Size --> CVC
    BG --> CVC
    Mask --> CVC
    
    CVC --> Output["Final Composite Clip"]
    CAC --> Output
```

Sources: [moviepy/video/compositing/CompositeVideoClip.py:12-198]()

## CompositeVideoClip

`CompositeVideoClip` is the base class for most compositions in MoviePy. It creates a single video clip by combining multiple video clips, with controls for positioning, layering, and transparency.

### Key Features

- **Layering System**: Clips with higher `layer_index` values appear on top of those with lower values
- **Positioning**: Each clip can be positioned anywhere within the frame using the `pos` attribute
- **Masking**: Supports transparency through clip masks
- **Audio Composition**: Automatically creates a composite audio track from the audio of the input clips
- **Background Options**: Can use a solid color background or the first clip as the background

```mermaid
classDiagram
    class VideoClip {
        +duration
        +start
        +end
        +get_frame()
    }
    
    class CompositeVideoClip {
        +clips: list
        +bg: VideoClip
        +bg_color
        +size
        +frame_function()
        +playing_clips()
    }
    
    VideoClip <|-- CompositeVideoClip
    CompositeVideoClip --> "n" VideoClip: contains
    
    class CompositeAudioClip {
        +clips: list
    }
    
    CompositeVideoClip --> "0..1" CompositeAudioClip: audio
```

Sources: [moviepy/video/compositing/CompositeVideoClip.py:12-198]()

### How CompositeVideoClip Works

When a `CompositeVideoClip` is created, it:

1. Sorts clips by their `layer_index` to determine the rendering order
2. Computes the overall duration based on clip end times (if all clips have durations)
3. Creates a composite audio track from the audio of all input clips
4. Generates a mask if transparency is needed
5. For each frame request:
   - Gets the frame from the background or creates a new one with the background color
   - Iterates through each playing clip and composes it onto the current frame
   - Applies masks if present

### Constructor Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `clips` | List | List of video clips to compose |
| `size` | Tuple | (width, height) of the final clip (defaults to first clip's size) |
| `bg_color` | Tuple/Float | Color for unfilled regions (None for transparency) |
| `use_bgclip` | Boolean | Whether to use the first clip as background |
| `is_mask` | Boolean | Whether this composite is being used as a mask |

Sources: [moviepy/video/compositing/CompositeVideoClip.py:56-128]()

## Compositing Functions

MoviePy provides specialized functions for common compositing tasks:

### clips_array

The `clips_array` function arranges clips in a grid layout (like a table or matrix). It takes a 2D array of clips and positions them in a grid format.

```mermaid
graph TD
    subgraph "clips_array Example"
        direction TB
        Input["Input: 2D array of clips"]
        Process["Process:
        1. Determine row widths and column heights
        2. Calculate positions
        3. Create CompositeVideoClip"]
        Output["Output: Grid-arranged clips"]
        
        Input --> Process --> Output
    end
    
    subgraph "Visual Representation"
        direction TB
        C11["Clip[0,0]"] --- C12["Clip[0,1]"] --- C13["Clip[0,2]"]
        C21["Clip[1,0]"] --- C22["Clip[1,1]"] --- C23["Clip[1,2]"]
        
        C11 --- C21
        C12 --- C22
        C13 --- C23
    end
```

The function automatically determines optimal row widths and column heights based on the clips' dimensions, or you can specify them manually. When clips don't fulfill the required dimensions, they're centered in their allocated cell.

Sources: [moviepy/video/compositing/CompositeVideoClip.py:199-265]()

### concatenate_videoclips

The `concatenate_videoclips` function combines clips temporally, playing them in sequence. It offers two methods:

1. **"chain"**: Simply plays clips one after another without modifying their contents
2. **"compose"**: Centers smaller clips in a frame large enough for all clips

```mermaid
graph LR
    subgraph "concatenate_videoclips Methods"
        direction TB
        Chain["Method: chain
        - Play clips sequentially
        - No resizing/repositioning
        - Original dimensions preserved"]
        
        Compose["Method: compose
        - Play clips sequentially
        - Center each clip in frame
        - Final size: widest × tallest"]
    end
    
    Clip1["Clip 1"] --> Chain
    Clip2["Clip 2"] --> Chain
    Clip3["Clip 3"] --> Chain
    
    Clip4["Clip 1"] --> Compose
    Clip5["Clip 2"] --> Compose
    Clip6["Clip 3"] --> Compose
    
    Chain --> Timeline1["|Clip 1|Clip 2|Clip 3|"]
    Compose --> Timeline2["|   Clip 1   |   Clip 2   |   Clip 3   |"]
```

Additional features of `concatenate_videoclips`:
- Transitions between clips
- Padding to control timing between clips
- Background color for unfilled areas in "compose" method

Sources: [moviepy/video/compositing/CompositeVideoClip.py:268-380]()

## Compositing Process Details

### Z-Order Rendering

Clips in a composition are rendered in order of their `layer_index` attribute. When multiple clips share the same layer, the clip appearing later in the `clips` list is displayed on top.

```mermaid
graph TD
    subgraph "Layer Rendering Order"
        direction BT
        L1["Layer 1 (bottom)"]
        L2["Layer 2"]
        L3["Layer 3 (top)"]
        
        Background --> L1 --> L2 --> L3 --> Final["Final Composite Frame"]
    end
```

Sources: [moviepy/video/compositing/CompositeVideoClip.py:26-30](), [moviepy/video/compositing/CompositeVideoClip.py:98]()

### Transparency and Masking

Compositing in MoviePy supports transparency through masks:

1. If a clip has a `mask` attribute, it determines which parts of the clip are visible
2. For `CompositeVideoClip`, a combined mask is created from the masks of all clips
3. When `bg_color` is set to `None`, the unfilled regions are transparent

The masking system is recursively applied, allowing for complex effects and overlays.

Sources: [moviepy/video/compositing/CompositeVideoClip.py:114-128]()

### Frame Composition

When a frame is requested from a `CompositeVideoClip`, the following process occurs:

1. Get a frame from the background (first clip or color background)
2. Determine which clips are playing at the current time
3. For each playing clip, in order of layer:
   - Get the frame from the clip at the current time
   - Apply the clip's mask if present
   - Blit the frame onto the result image at the clip's position
4. Return the composed frame

This process is handled by the `frame_function` method, which uses PIL for image composition.

Sources: [moviepy/video/compositing/CompositeVideoClip.py:130-179]()

## Usage Examples

Here are some common compositing scenarios in MoviePy:

### Basic Overlay

To place a smaller clip on top of a larger background clip:

```python
from moviepy.editor import VideoFileClip, CompositeVideoClip

background = VideoFileClip("background.mp4")
overlay = VideoFileClip("overlay.mp4").resize(0.5)  # 50% of original size
overlay = overlay.set_pos(("center", "center"))  # Center position

final_clip = CompositeVideoClip([background, overlay])
final_clip.write_videofile("output.mp4")
```

### Grid Layout

To arrange clips in a grid:

```python
from moviepy.editor import VideoFileClip, clips_array

clip1 = VideoFileClip("clip1.mp4")
clip2 = VideoFileClip("clip2.mp4")
clip3 = VideoFileClip("clip3.mp4")
clip4 = VideoFileClip("clip4.mp4")

final_clip = clips_array([[clip1, clip2], 
                          [clip3, clip4]])
final_clip.write_videofile("grid_output.mp4")
```

### Sequential Playback

To play clips one after another:

```python
from moviepy.editor import VideoFileClip, concatenate_videoclips

clip1 = VideoFileClip("clip1.mp4")
clip2 = VideoFileClip("clip2.mp4")
clip3 = VideoFileClip("clip3.mp4")

final_clip = concatenate_videoclips([clip1, clip2, clip3], method="chain")
final_clip.write_videofile("sequence_output.mp4")
```

## Related Systems

The compositing system interacts with several other MoviePy components:

1. **Effect System**: Effects can be applied to clips before composition. For more details, see [Video Effects](#3.1) and [Audio Effects](#3.2).
2. **I/O System**: The resulting composite clips can be written to files or previewed. See [Writing Media](#4.3).
3. **Audio Compositing**: When video clips with audio are composed, their audio tracks are automatically composed using `CompositeAudioClip`. See [AudioClip](#2.3).

## Performance Considerations

Compositing operations can be memory-intensive, especially with large videos or complex compositions. Some best practices include:

- Use clip resizing when appropriate to reduce memory usage
- Consider using lower-resolution previews during development
- For sequential compositions with many clips, the "chain" method is more memory-efficient
- When working with masks and transparency, setting explicit durations for clips can improve performance

## Conclusion

The compositing system in MoviePy provides powerful tools for combining video clips both spatially and temporally. Through the `CompositeVideoClip` class and utility functions like `clips_array` and `concatenate_videoclips`, users can create complex video compositions with precise control over positioning, layering, and timing.