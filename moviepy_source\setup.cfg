[flake8]
max-line-length = 88
extend-ignore =
    # Black compatibility
    E203,
    W503,
    # allow lambda expressions
    E731,
    # don't require docstrings for public packages
    D104,
    # don't require docstrings for magic methods
    D105,
    # don't require summary and description in docstrings
    D205,
    # allow first line of docstrings not ending in period (too much limited)
    D400,
    # allow first line of docstrings being not imperative (too much intrusive)
    D401,
    # allow blank lines between section headers and their content in docstrings
    D412,
    # allow composed `__all__` statements
    RST902,
    # allow 'from moviepy import *' in editor.py
    F403, F405
per-file-ignores =
    # allow imports not placed at the top of the file
    # allow 'from moviepy import *' in editor.py
    moviepy/editor.py: E402, F403, F405
    # the version file doesn't require module docstring
    moviepy/version.py: D100
    # FX modules don't require module docstring
    moviepy/audio/fx/*.py: D100
    moviepy/video/fx/*.py: D100
    # tests doesn't require docstring (although is recommended)
    tests/*.py: D101,D102,D103
    # examples don't require module docstring
    # allow 'from moviepy import *' in examples
    examples/*.py: D100, F403, F405
docstring-convention = numpy

# Complexity should be decreased before uncomment:
#max-complexity = 10

[isort]
profile = black
lines_after_imports = 2
combine_as_imports = True
py_version = 39
known_tests_third_party = pytest
sections = STDLIB,THIRDPARTY,TESTS_THIRD_PARTY,FIRSTPARTY,LOCALFOLDER

[coverage:report]
exclude_lines =
    pragma: no cover
