#!/usr/bin/env python3
"""
测试真实的多行歌词文件
使用项目中的英文LRC文件来验证多行歌词支持
"""

import os
from pathlib import Path
from lyric_timeline import LyricTimeline, LyricDisplayMode
from enhanced_generator import Enhanced<PERSON>ingwuGenerator

def test_real_english_lrc():
    """测试真实的英文LRC文件"""
    print("🔍 测试真实英文LRC文件的多行歌词解析")
    print("=" * 60)

    english_lrc_path = Path("精武英雄/Jingwu Hero - Donnie Yen.lrc")

    if not english_lrc_path.exists():
        print("❌ 英文LRC文件不存在")
        return None

    # 解析LRC文件
    timeline = LyricTimeline.from_lrc_file(
        str(english_lrc_path),
        language="english",
        display_mode=LyricDisplayMode.SIMPLE_FADE
    )

    print(f"✅ 成功解析英文LRC文件，共 {len(timeline.lyrics_data)} 条歌词")

    # 显示前10条歌词，重点关注多行歌词
    print("\n📝 前10条歌词内容:")
    for i, (timestamp, text) in enumerate(timeline.lyrics_data[:10]):
        lines = text.split('\n')
        if len(lines) > 1:
            print(f"🔸 [{timestamp:06.2f}s] 多行歌词 ({len(lines)} 行):")
            for j, line in enumerate(lines):
                print(f"    {j+1}: {line}")
        else:
            print(f"🔹 [{timestamp:06.2f}s] 单行: {text}")

    # 统计多行歌词
    multiline_count = 0
    max_lines = 1
    for _, text in timeline.lyrics_data:
        lines = text.split('\n')
        line_count = len([line for line in lines if line.strip()])
        if line_count > 1:
            multiline_count += 1
            max_lines = max(max_lines, line_count)

    print(f"\n📊 统计信息:")
    print(f"   总歌词数: {len(timeline.lyrics_data)}")
    print(f"   多行歌词数: {multiline_count}")
    print(f"   最大行数: {max_lines}")

    return timeline

def test_layout_calculation(timeline):
    """测试布局计算"""
    print("\n📐 测试布局计算")
    print("=" * 50)

    video_width, video_height = 720, 1280

    # 测试简单模式
    timeline.set_display_mode(LyricDisplayMode.SIMPLE_FADE)
    simple_rect = timeline.calculate_required_rect(video_width, video_height)
    print(f"✅ 简单模式区域: {simple_rect}")

    # 测试增强预览模式
    timeline.set_display_mode(LyricDisplayMode.ENHANCED_PREVIEW)
    enhanced_rect = timeline.calculate_required_rect(video_width, video_height)
    print(f"✅ 增强模式区域: {enhanced_rect}")

    # 比较单行和多行的区域差异
    print(f"\n📏 区域分析:")
    print(f"   简单模式高度: {simple_rect.height}px")
    print(f"   增强模式高度: {enhanced_rect.height}px")
    print(f"   高度比例: {enhanced_rect.height / simple_rect.height:.2f}")

def debug_layout_engine():
    """调试布局引擎的详细计算过程"""
    print("\n🔍 调试布局引擎计算过程")
    print("=" * 60)

    # 检查必要文件
    english_lrc = Path("精武英雄/Jingwu Hero - Donnie Yen.lrc")
    chinese_lrc = Path("精武英雄/精武英雄 - 甄子丹.lrc")

    if not all([english_lrc.exists(), chinese_lrc.exists()]):
        print("❌ 缺少必要的LRC文件")
        return

    # 创建时间轴（与实际视频生成相同的配置）
    chinese_timeline = LyricTimeline.from_lrc_file(
        str(chinese_lrc),
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW
    )

    english_timeline = LyricTimeline.from_lrc_file(
        str(english_lrc),
        language="english",
        display_mode=LyricDisplayMode.SIMPLE_FADE
    )

    video_width, video_height = 720, 1280

    print(f"🔧 视频尺寸: {video_width}x{video_height}")
    print(f"🔧 中文时间轴最大行数: {chinese_timeline.max_lines}")
    print(f"🔧 英文时间轴最大行数: {english_timeline.max_lines}")

    # 计算原始区域
    chinese_rect = chinese_timeline.calculate_required_rect(video_width, video_height)
    english_rect = english_timeline.calculate_required_rect(video_width, video_height)

    print(f"\n📐 原始区域计算:")
    print(f"   中文时间轴: {chinese_rect}")
    print(f"   英文时间轴: {english_rect}")

    # 检查重叠
    overlaps = chinese_rect.overlaps_with(english_rect)
    print(f"\n🔍 重叠检测:")
    print(f"   是否重叠: {overlaps}")

    if overlaps:
        print(f"   中文区域范围: Y={chinese_rect.y} 到 Y={chinese_rect.y + chinese_rect.height}")
        print(f"   英文区域范围: Y={english_rect.y} 到 Y={english_rect.y + english_rect.height}")

        # 计算重叠区域
        overlap_start = max(chinese_rect.y, english_rect.y)
        overlap_end = min(chinese_rect.y + chinese_rect.height, english_rect.y + english_rect.height)
        overlap_height = max(0, overlap_end - overlap_start)
        print(f"   重叠区域: Y={overlap_start} 到 Y={overlap_end}, 高度={overlap_height}px")

    # 测试布局引擎
    print(f"\n🔧 测试布局引擎:")
    from layout_engine import LayoutEngine, VerticalStackStrategy

    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
    layout_engine.add_element(chinese_timeline)
    layout_engine.add_element(english_timeline)

    # 检测冲突（使用原始区域）
    conflicts = layout_engine.detect_conflicts(video_width, video_height)
    print(f"   冲突检测结果: {conflicts}")

    # 计算布局
    layout_result = layout_engine.calculate_layout(video_width, video_height)
    print(f"   布局计算结果:")
    for element_id, rect in layout_result.element_positions.items():
        print(f"     {element_id}: {rect}")

    # 验证布局后是否还有重叠
    layout_rects = list(layout_result.element_positions.values())
    if len(layout_rects) >= 2:
        final_overlaps = layout_rects[0].overlaps_with(layout_rects[1])
        print(f"   布局后是否重叠: {final_overlaps}")

        if not final_overlaps:
            print(f"   ✅ 布局引擎成功解决了重叠问题")
            rect1, rect2 = layout_rects[0], layout_rects[1]
            gap = abs((rect1.y + rect1.height) - rect2.y) if rect1.y < rect2.y else abs((rect2.y + rect2.height) - rect1.y)
            print(f"   元素间距: {gap}px")
        else:
            print(f"   ❌ 布局引擎未能解决重叠问题")

def test_video_generation_with_real_files():
    """使用真实文件测试视频生成"""
    print("\n🎬 测试真实文件视频生成")
    print("=" * 50)

    # 检查必要文件
    english_lrc = Path("精武英雄/Jingwu Hero - Donnie Yen.lrc")
    chinese_lrc = Path("精武英雄/精武英雄 - 甄子丹.lrc")
    audio_file = Path("精武英雄/精武英雄 - 甄子丹.flac")

    if not all([english_lrc.exists(), chinese_lrc.exists(), audio_file.exists()]):
        print("❌ 缺少必要的文件，跳过视频生成测试")
        return False

    try:
        # 创建时间轴
        english_timeline = LyricTimeline.from_lrc_file(
            str(english_lrc),
            language="english",
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )

        chinese_timeline = LyricTimeline.from_lrc_file(
            str(chinese_lrc),
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )

        # 创建生成器
        generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)

        output_path = "test_real_multiline_output.mp4"

        print("🔧 开始生成双语多行歌词视频...")

        # 生成视频（限制时长以加快测试）
        success = generator.generate_bilingual_video(
            main_timeline=chinese_timeline,
            aux_timeline=english_timeline,
            audio_path=str(audio_file),
            output_path=output_path,
            background_image=None,
            t_max_sec=30.0  # 只生成前30秒
        )

        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"✅ 视频生成成功！")
            print(f"📁 输出文件: {output_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            return True
        else:
            print("❌ 视频生成失败")
            return False

    except Exception as e:
        print(f"❌ 视频生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎵 真实多行歌词支持测试")
    print("=" * 70)

    try:
        # 1. 测试真实LRC解析
        timeline = test_real_english_lrc()
        if not timeline:
            print("❌ LRC解析测试失败")
            return

        # 2. 测试布局计算
        test_layout_calculation(timeline)

        # 3. 调试布局引擎
        debug_layout_engine()

        # 4. 测试视频生成
        video_success = test_video_generation_with_real_files()

        # 总结
        print("\n🎉 测试总结")
        print("=" * 50)
        print("✅ 真实LRC解析: 成功处理相同时间点多条记录")
        print("✅ 布局计算: 正确计算多行文本区域")
        print(f"{'✅' if video_success else '❌'} 视频生成: {'成功' if video_success else '失败'}")

        if video_success:
            print("\n💡 多行歌词支持验证完成！")
            print("   ✨ 英文LRC中的多行歌词已正确合并和显示")
            print("   ✨ 布局引擎正确处理多行文本的空间需求")
            print("   ✨ 视频渲染支持换行符的正确显示")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
