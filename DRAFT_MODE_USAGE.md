# 草稿模式使用指南

## 概述

草稿模式是为开发和测试阶段设计的快速视频渲染功能，通过使用硬件编码器和优化的编码参数，显著减少视频生成时间，适合功能开发和测试过程中的快速迭代。

## 主要特性

- 🚀 **硬件加速**: 优先使用NVENC硬件编码器
- ⚡ **快速编码**: 使用快速预设和较低质量参数
- 🔄 **自动回退**: NVENC失败时自动回退到软件编码
- ⏱️ **性能监控**: 显示编码耗时和详细信息
- 🛡️ **Fast Fail**: 开发环境下快速失败，便于调试

## 使用方法

### 1. 基本API使用

```python
from enhanced_generator import EnhancedJingwuGenerator
from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle

# 创建生成器
generator = EnhancedJingwuGenerator()

# 创建时间轴
timeline = LyricTimeline.from_lrc_file(
    "lyrics.lrc",
    language="chinese",
    display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
    style=LyricStyle(font_size=80)
)

# 草稿模式生成
success = generator.generate_bilingual_video(
    main_timeline=timeline,
    audio_path="audio.mp3",
    output_path="draft_output.mp4",
    t_max_sec=20.0,  # 限制测试时长
    draft_mode=True  # 启用草稿模式
)
```

### 2. 配置文件使用

```python
from pathlib import Path
from enhanced_generator import demo_draft_mode, demo_enhanced_features

# 草稿模式 - 快速测试
demo_draft_mode(Path("config.yaml"), t_max_sec=20.0)

# 产品模式 - 最终输出
demo_enhanced_features(Path("config.yaml"), draft_mode=False)
```

### 3. 性能对比测试

```python
# 运行性能对比测试
python test_draft_mode.py
```

## 编码配置对比

| 模式 | 编码器 | 预设 | 质量参数 | 适用场景 |
|------|--------|------|----------|----------|
| 草稿模式 | h264_nvenc | fast | -cq 28 | 开发测试 |
| 草稿回退 | libx264 | ultrafast | -crf 28 | NVENC不可用 |
| 产品模式 | libx264 | medium | -crf 18 | 最终输出 |

## 输出示例

### 草稿模式输出
```
🚀 草稿模式演示 - 快速编码
==================================================
注意: 草稿模式使用快速编码设置，质量较低但速度更快，适合开发测试使用

精武英雄歌词视频生成器 - 纯OOP版
==================================================
...
   🚀 使用草稿质量配置进行快速编码...
导出视频到: output.mp4
   编码器: h264_nvenc, 预设: fast, 参数: ['-cq', '28']
✅ 视频导出完成 (草稿模式): 15.32 秒
```

### 产品模式输出
```
   🎬 使用产品质量配置进行编码...
导出视频到: output.mp4
   编码器: libx264, 预设: medium, 参数: ['-crf', '18']
✅ 视频导出完成 (产品模式): 45.67 秒
```

## 错误处理

### NVENC不可用时的自动回退
```
⚠️  NVENC编码失败 (Codec 'h264_nvenc' not found)，回退到软件编码...
✅ 视频导出完成 (草稿模式): 28.45 秒
```

## 最佳实践

### 开发阶段
1. **使用草稿模式**: 快速验证功能和效果
2. **限制时长**: 使用`t_max_sec=20.0`等参数限制测试时长
3. **监控性能**: 关注编码耗时，优化开发流程

```python
# 开发测试推荐设置
demo_draft_mode(Path("config.yaml"), t_max_sec=10.0)
```

### 生产阶段
1. **使用产品模式**: 确保最终输出质量
2. **完整时长**: 生成完整视频内容
3. **质量检查**: 验证最终输出

```python
# 生产环境推荐设置
demo_enhanced_features(Path("config.yaml"), draft_mode=False)
```

## 硬件要求

### NVENC支持
- **GPU**: 支持NVENC的NVIDIA显卡
- **驱动**: 最新的NVIDIA驱动程序
- **FFmpeg**: 编译时包含NVENC支持

### 检查NVENC可用性
```bash
ffmpeg -encoders | grep nvenc
```

如果看到类似输出，说明NVENC可用：
```
V..... h264_nvenc         NVIDIA NVENC H.264 encoder
V..... hevc_nvenc         NVIDIA NVENC hevc encoder
```

## 性能预期

基于测试环境的典型性能表现：

| 视频时长 | 草稿模式 | 产品模式 | 加速比 |
|----------|----------|----------|--------|
| 10秒 | 8-15秒 | 25-40秒 | 2-3x |
| 30秒 | 20-35秒 | 60-120秒 | 2-4x |
| 60秒 | 40-70秒 | 120-240秒 | 2-4x |

*注意: 实际性能取决于硬件配置、视频复杂度和系统负载*

## 故障排除

### 常见问题

1. **NVENC不可用**
   - 检查GPU是否支持NVENC
   - 更新NVIDIA驱动程序
   - 确认FFmpeg编译包含NVENC支持

2. **编码失败**
   - 检查输出路径是否可写
   - 确认音频文件格式支持
   - 查看详细错误信息

3. **性能提升不明显**
   - 确认使用了硬件编码器
   - 检查系统资源使用情况
   - 考虑调整编码参数

### 调试模式

启用详细日志输出：
```python
# 在生成器中启用详细输出
generator.generate_bilingual_video(
    ...,
    draft_mode=True
)
# 查看控制台输出的编码器和参数信息
```

## 扩展配置

### 自定义编码参数

```python
# 自定义草稿模式参数
custom_params = ['-cq', '30', '-preset', 'faster']

# 直接调用底层方法
generator._finalize_and_export_video(
    all_clips=clips,
    audio_clip=audio,
    output_path="output.mp4",
    ffmpeg_params_custom=custom_params,
    draft_mode=True
)
```

### 质量vs速度平衡

调整CQ值来平衡质量和速度：
- CQ 20-25: 较高质量，较慢速度
- CQ 28-30: 平衡质量和速度（推荐）
- CQ 32-35: 较低质量，较快速度
