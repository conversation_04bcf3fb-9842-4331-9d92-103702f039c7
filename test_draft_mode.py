#!/usr/bin/env python3
"""
草稿模式测试脚本
测试新增的草稿质量渲染功能
"""

import time
from pathlib import Path
from enhanced_generator import EnhancedJingwuGenerator, demo_enhanced_features, demo_draft_mode
from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle

def test_draft_vs_production_comparison():
    """对比草稿模式和产品模式的性能差异"""
    print("=" * 60)
    print("草稿模式 vs 产品模式性能对比测试")
    print("=" * 60)
    
    # 测试配置
    config_path = Path(r"精武英雄\lrc-mv.yaml")
    test_duration = 10.0  # 10秒测试视频
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        print("请确保配置文件存在后再运行测试")
        return
    
    # 测试1: 草稿模式
    print("\n🚀 测试1: 草稿模式")
    print("-" * 30)
    draft_start = time.perf_counter()
    
    try:
        success_draft = demo_enhanced_features(
            config_path, 
            t_max_sec=test_duration, 
            draft_mode=True
        )
        draft_end = time.perf_counter()
        draft_time = draft_end - draft_start
        
        if success_draft:
            print(f"✅ 草稿模式完成: {draft_time:.2f} 秒")
        else:
            print("❌ 草稿模式失败")
            return
            
    except Exception as e:
        print(f"❌ 草稿模式异常: {e}")
        return
    
    # 等待一下，避免文件冲突
    time.sleep(2)
    
    # 测试2: 产品模式
    print("\n🎬 测试2: 产品模式")
    print("-" * 30)
    production_start = time.perf_counter()
    
    try:
        success_production = demo_enhanced_features(
            config_path, 
            t_max_sec=test_duration, 
            draft_mode=False
        )
        production_end = time.perf_counter()
        production_time = production_end - production_start
        
        if success_production:
            print(f"✅ 产品模式完成: {production_time:.2f} 秒")
        else:
            print("❌ 产品模式失败")
            return
            
    except Exception as e:
        print(f"❌ 产品模式异常: {e}")
        return
    
    # 性能对比
    print("\n📊 性能对比结果")
    print("=" * 30)
    print(f"草稿模式耗时: {draft_time:.2f} 秒")
    print(f"产品模式耗时: {production_time:.2f} 秒")
    
    if draft_time < production_time:
        speedup = production_time / draft_time
        print(f"🚀 草稿模式加速比: {speedup:.2f}x")
        print(f"⏱️  节省时间: {production_time - draft_time:.2f} 秒")
    else:
        print("⚠️  草稿模式未显示明显加速效果")

def test_nvenc_availability():
    """测试NVENC硬件编码器可用性"""
    print("\n🔧 NVENC硬件编码器可用性测试")
    print("-" * 40)
    
    # 创建一个简单的测试时间轴
    generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)
    
    # 创建测试用的简单时间轴
    test_style = LyricStyle(font_size=60, font_color='white')
    test_timeline = LyricTimeline(
        lyrics=[("测试歌词", 0.0, 2.0)],
        language="chinese",
        display_mode=LyricDisplayMode.SIMPLE_FADE,
        style=test_style
    )
    
    # 测试音频文件路径（如果存在的话）
    test_audio = Path(r"精武英雄\audio.mp3")
    if not test_audio.exists():
        print("⚠️  测试音频文件不存在，跳过NVENC测试")
        return
    
    # 创建测试输出路径
    test_output = Path("test_nvenc_output.mp4")
    
    try:
        print("正在测试NVENC编码器...")
        success = generator.generate_bilingual_video(
            main_timeline=test_timeline,
            audio_path=str(test_audio),
            output_path=str(test_output),
            t_max_sec=2.0,
            draft_mode=True  # 使用草稿模式，会尝试NVENC
        )
        
        if success and test_output.exists():
            print("✅ NVENC编码器可用")
            # 清理测试文件
            test_output.unlink()
        else:
            print("❌ NVENC编码器不可用或测试失败")
            
    except Exception as e:
        print(f"❌ NVENC测试异常: {e}")

def main():
    """主测试函数"""
    print("草稿模式功能测试")
    print("=" * 60)
    
    # 测试NVENC可用性
    test_nvenc_availability()
    
    # 性能对比测试
    test_draft_vs_production_comparison()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
