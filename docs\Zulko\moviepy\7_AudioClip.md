# AudioClip

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/audio/AudioClip.py](moviepy/audio/AudioClip.py)
- [moviepy/audio/io/AudioFileClip.py](moviepy/audio/io/AudioFileClip.py)
- [moviepy/video/io/VideoFileClip.py](moviepy/video/io/VideoFileClip.py)

</details>



## Introduction to AudioClip

The `AudioClip` class is a fundamental component in MoviePy that provides the foundation for all audio handling capabilities. It serves as the base class for creating, manipulating, and outputting audio in the MoviePy library.

For video handling and processing, see [VideoClip](#2.2).

This document covers:
- The `AudioClip` base class and its core functionality
- Subclasses that provide specialized audio capabilities
- Methods for reading, writing, and previewing audio
- How audio clips integrate with other MoviePy components

Sources: [moviepy/audio/AudioClip.py:20-313]()

## AudioClip Architecture

The `AudioClip` system follows a hierarchical design where all audio functionality extends from the base `AudioClip` class.

```mermaid
classDiagram
    class Clip {
        +duration
        +start
        +end
        +transform()
        +time_transform()
        +with_effects()
        +subclipped()
    }
    
    class AudioClip {
        +frame_function
        +fps
        +nchannels
        +to_soundarray()
        +write_audiofile()
        +audiopreview()
        +max_volume()
        +iter_chunks()
    }
    
    class AudioArrayClip {
        +array
        +fps
        +duration
    }
    
    class AudioFileClip {
        +filename
        +reader
        +buffersize
        +close()
    }
    
    class CompositeAudioClip {
        +clips
        +starts
        +ends
        +frame_function()
    }
    
    Clip <|-- AudioClip
    AudioClip <|-- AudioArrayClip
    AudioClip <|-- AudioFileClip
    AudioClip <|-- CompositeAudioClip
```

Sources: [moviepy/audio/AudioClip.py:20-421](), [moviepy/audio/io/AudioFileClip.py:8-85]()

## AudioClip Base Class

The `AudioClip` class represents an audio segment with a customizable duration, characterized by a frame function that transforms time values to sound samples.

### Core Design

An `AudioClip` is fundamentally a `Clip` that adds audio-specific functionality:

```mermaid
flowchart TD
    subgraph "AudioClip Core Design"
        TimeValuesT["Time values (t)"] --> FrameFunction["frame_function(t)"]
        FrameFunction --> SoundSamples["Sound samples"]
        SoundSamples --> AudioProcessing["Processing & Output"]
        
        subgraph "Audio Processing & Output"
            ToSoundArray["to_soundarray()"]
            WriteAudioFile["write_audiofile()"]
            AudioPreview["audiopreview()"]
        end
    end
```

The frame function takes a time value `t` and returns a sound frame at that time. For mono sound, it returns an array of floats `[f_t]` and for stereo sound, it returns an array of the form `[f1_t, f2_t]` where the values are floats between -1 and 1.

Sources: [moviepy/audio/AudioClip.py:20-84]()

### Key Attributes

- `frame_function`: A function that returns audio samples for a given time `t`
- `fps`: Frames per second (sample rate) for audio processing
- `nchannels`: Number of audio channels (1 for mono, 2 for stereo)
- `duration`: Length of the audio clip in seconds

Sources: [moviepy/audio/AudioClip.py:68-84]()

### Core Methods

#### Converting to Sound Array

The `to_soundarray()` method transforms the audio clip into an array that can be played or written to a file:

```python
clip.to_soundarray(fps=44100, nbytes=2, quantize=False)
```

This method processes the audio frame by frame according to the specified frame rate (fps) and bit depth (nbytes).

Sources: [moviepy/audio/AudioClip.py:117-167]()

#### Writing Audio Files

The `write_audiofile()` method outputs the audio clip to a file:

```python
clip.write_audiofile("output.wav", fps=44100, nbytes=2, codec="pcm_s16le")
```

This method leverages FFmpeg for audio encoding and supports various audio formats based on the file extension.

Sources: [moviepy/audio/AudioClip.py:184-269]()

#### Audio Preview

The `audiopreview()` method allows for instant audible playback of the clip:

```python
clip.audiopreview(fps=44100, nbytes=2)
```

This utilizes FFplay to play the audio without needing to save it first.

Sources: [moviepy/audio/AudioClip.py:272-308]()

## AudioClip Subclasses

MoviePy provides three specialized subclasses of `AudioClip` for different audio sources and composition.

### AudioArrayClip

`AudioArrayClip` creates an audio clip from a NumPy array:

```python
from numpy import sin, pi, arange
fps = 44100
duration = 3
array = sin(2*pi*440*arange(0, duration, 1.0/fps))
clip = AudioArrayClip(array, fps=fps)
```

The audio data is stored directly in memory as a NumPy array.

Sources: [moviepy/audio/AudioClip.py:316-358]()

### AudioFileClip

`AudioFileClip` reads audio from a file without loading the entire file into memory:

```python
clip = AudioFileClip("soundtrack.mp3")
```

It uses FFMPEG_AudioReader to read chunks of the audio file on demand.

Sources: [moviepy/audio/io/AudioFileClip.py:8-85]()

### CompositeAudioClip

`CompositeAudioClip` combines multiple audio clips, potentially overlapping and starting at different times:

```python
composite = CompositeAudioClip([clip1, clip2.with_start(5)])
```

The frame function of a `CompositeAudioClip` combines the frames from its component clips based on their start times.

Sources: [moviepy/audio/AudioClip.py:361-420]()

## Audio Operations and Composition

### Chunked Audio Processing

Audio clips are processed in chunks to handle large files efficiently:

```mermaid
flowchart LR
    AudioClip["AudioClip"] --> InternalProcessing["Processing in chunks"]
    InternalProcessing --> OutputIterations["iter_chunks()"]
    OutputIterations --> to_soundarray["to_soundarray()"]
    to_soundarray --> Output["Audio Output"]
```

The `iter_chunks()` method allows for processing audio in manageable segments rather than loading the entire file into memory.

Sources: [moviepy/audio/AudioClip.py:86-114]()

### Audio Concatenation

The `concatenate_audioclips()` function joins multiple audio clips sequentially:

```python
final_clip = concatenate_audioclips([clip1, clip2, clip3])
```

This creates a new `CompositeAudioClip` where each clip starts after the previous one ends.

Sources: [moviepy/audio/AudioClip.py:423-437]()

### Volume Analysis

The `max_volume()` method determines the maximum volume level of a clip:

```python
max_level = clip.max_volume(stereo=True)
```

This is useful for normalizing audio or checking for clipping.

Sources: [moviepy/audio/AudioClip.py:169-180]()

## Integration with MoviePy

### Relationship with VideoClip

`AudioClip` integrates with `VideoClip` through the `audio` attribute:

```mermaid
flowchart TD
    subgraph "Video and Audio Integration"
        VideoFileClip["VideoFileClip"] --> HasAudio{"Has Audio Track?"}
        HasAudio -->|"Yes"| AudioFileClip["Create AudioFileClip"]
        AudioFileClip --> SetAudioAttribute["video_clip.audio = audio_clip"]
        HasAudio -->|"No"| NoAudio["No audio attribute set"]
    end
```

When a video file with audio is loaded, MoviePy creates an `AudioFileClip` instance and assigns it to the `audio` attribute of the `VideoFileClip`.

Sources: [moviepy/video/io/VideoFileClip.py:143-149]()

### Audio in the MoviePy Pipeline

```mermaid
flowchart LR
    subgraph "Audio Processing Pipeline"
        AudioSource["Audio Source"] --> AudioClipCreation["AudioClip Creation"]
        AudioClipCreation --> AudioProcessing["Audio Processing"]
        AudioProcessing --> AudioOutput["Audio Output"]
    end
    
    subgraph "Audio Source"
        Files["Audio Files"]
        Arrays["NumPy Arrays"]
        VideoFiles["Audio from Video"]
    end
    
    subgraph "AudioClip Creation"
        AudioFileClip["AudioFileClip"]
        AudioArrayClip["AudioArrayClip"]
        CompositeAudioClip["CompositeAudioClip"]
    end
    
    subgraph "Audio Processing"
        Effects["Audio Effects"]
        Concatenation["Concatenation"]
        Composition["Composition"]
    end
    
    subgraph "Audio Output"
        WriteAudioFile["write_audiofile()"]
        Preview["audiopreview()"]
        ToVideoAudio["Used as video.audio"]
    end
```

Audio can enter the MoviePy pipeline from various sources, be processed through `AudioClip` instances, and eventually be output as standalone audio files or as part of video files.

Sources: [moviepy/audio/AudioClip.py:20-421](), [moviepy/audio/io/AudioFileClip.py:8-85](), [moviepy/video/io/VideoFileClip.py:143-149]()

## Usage Examples

### Creating a Simple AudioClip

Create a sine wave audio clip:

```python
import numpy as np
from moviepy.audio.AudioClip import AudioClip

# Create a 440 Hz sine wave
frame_function = lambda t: np.sin(440 * 2 * np.pi * t)
clip = AudioClip(frame_function, duration=5, fps=44100)
clip.preview()
```

Sources: [moviepy/audio/AudioClip.py:50-57]()

### Stereo Audio Example

Create a stereo audio clip with different frequencies in each channel:

```python
import numpy as np
from moviepy.audio.AudioClip import AudioClip

# Create stereo audio with 440 Hz in left channel and 880 Hz in right channel
frame_function = lambda t: np.array([
    np.sin(440 * 2 * np.pi * t),
    np.sin(880 * 2 * np.pi * t)
]).T.copy(order="C")
clip = AudioClip(frame_function, duration=3, fps=44100)
clip.preview()
```

Sources: [moviepy/audio/AudioClip.py:58-64]()

### Using Audio Files

Load an audio file and extract a segment:

```python
from moviepy.audio.io.AudioFileClip import AudioFileClip

clip = AudioFileClip("soundtrack.mp3")
clip_segment = clip.subclip(10, 20)  # Extract seconds 10-20
clip_segment.write_audiofile("segment.mp3")
clip.close()  # Important to avoid resource leaks
```

Sources: [moviepy/audio/io/AudioFileClip.py:54-55]()

### Compositing Audio Clips

Create a composite audio by combining multiple clips:

```python
from moviepy.audio.io.AudioFileClip import AudioFileClip
from moviepy.audio.AudioClip import CompositeAudioClip

clip1 = AudioFileClip("music.mp3")
clip2 = AudioFileClip("voiceover.wav").with_start(5)  # Starts at 5 seconds
composite = CompositeAudioClip([clip1, clip2])
composite.write_audiofile("composite.mp3")
clip1.close()
clip2.close()
```

Sources: [moviepy/audio/AudioClip.py:361-420]()