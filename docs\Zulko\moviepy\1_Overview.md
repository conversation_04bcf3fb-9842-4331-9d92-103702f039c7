# Overview

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [.gitignore](.gitignore)
- [CHANGELOG.md](CHANGELOG.md)
- [LICENCE.txt](LICENCE.txt)
- [README.md](README.md)
- [docs/Makefile](docs/Makefile)
- [docs/getting_started/quick_presentation.rst](docs/getting_started/quick_presentation.rst)
- [docs/index.rst](docs/index.rst)
- [moviepy/Clip.py](moviepy/Clip.py)
- [moviepy/version.py](moviepy/version.py)
- [pyproject.toml](pyproject.toml)
- [tests/test_BitmapClip.py](tests/test_BitmapClip.py)
- [tests/test_Clip.py](tests/test_Clip.py)
- [tests/test_TextClip.py](tests/test_TextClip.py)
- [tests/test_VideoClip.py](tests/test_VideoClip.py)
- [tests/test_fx.py](tests/test_fx.py)

</details>



MoviePy is a Python library for video editing, processing, and creation. It provides a flexible, intuitive API for tasks such as cutting, concatenating, overlaying, and applying effects to videos and audio. MoviePy is designed to make complex video editing tasks accessible through code, enabling automation of video production workflows, creation of dynamic content, and integration of video processing into Python applications.

## Purpose and Scope

This document provides a high-level overview of MoviePy's architecture, key concepts, and components. It explains how the library is structured, how different parts interact, and the workflow for creating and manipulating video content. For specific implementation details, refer to related pages:

- [Getting Started](#1.1) - Quick introduction with basic examples
- [Installation and Requirements](#1.2) - Setup instructions
- [The Clip System](#2.1) - In-depth information on clip architecture
- [VideoClip](#2.2) - Working with video clips
- [AudioClip](#2.3) - Working with audio clips
- [Compositing](#3.3) - Combining multiple clips

## Core Architecture: The Clip System

The central concept in MoviePy is the `Clip`, defined in the base `Clip` class. This provides the foundation for all media elements, with two main subclasses: `VideoClip` for visual content and `AudioClip` for audio content.

### Class Hierarchy

```mermaid
classDiagram
    class "Clip" {
        +duration: float
        +start: float
        +end: float
        +get_frame(t): array
        +transform(func): Clip
        +time_transform(time_func): Clip
        +with_effects(effects): Clip
        +subclipped(start_time, end_time): Clip
    }
    
    "Clip" <|-- "VideoClip"
    "Clip" <|-- "AudioClip"
    
    "VideoClip" <|-- "ImageClip"
    "VideoClip" <|-- "ColorClip"
    "VideoClip" <|-- "TextClip"
    "VideoClip" <|-- "BitmapClip"
    "VideoClip" <|-- "VideoFileClip"
    "VideoClip" <|-- "CompositeVideoClip"
    
    "AudioClip" <|-- "AudioFileClip"
    "AudioClip" <|-- "AudioArrayClip"
    "AudioClip" <|-- "CompositeAudioClip"
    
    class "VideoClip" {
        +size: tuple
        +mask: VideoClip
        +audio: AudioClip
        +get_frame(t): array
        +write_videofile(filename): None
        +preview(): None
    }
    
    class "AudioClip" {
        +fps: int
        +get_frame(t): array
        +to_soundarray(): array
        +write_audiofile(filename): None
    }
```

Sources: [moviepy/Clip.py](), [tests/test_Clip.py]()

### Key Clip Types

MoviePy provides several specialized clip types:

1. **VideoFileClip**: For reading video files
2. **ImageClip**: For static images used as video
3. **ColorClip**: For generating solid color backgrounds
4. **TextClip**: For creating text overlays
5. **BitmapClip**: For creating video from character arrays
6. **CompositeVideoClip**: For overlaying multiple video clips
7. **AudioFileClip**: For reading audio files
8. **CompositeAudioClip**: For mixing multiple audio clips

Each clip type serves a specific purpose but shares the common `Clip` interface with methods like `get_frame()`, `subclipped()`, and `with_effects()`.

Sources: [tests/test_VideoClip.py](), [tests/test_TextClip.py](), [tests/test_BitmapClip.py]()

## Media Processing Pipeline

The MoviePy workflow follows a clear pipeline from input media to output:

```mermaid
flowchart LR
    "Input" --> "Clips" --> "Transform" --> "Composite" --> "Output"
    
    subgraph "Input"
        "VideoFiles"["Video Files"]
        "ImageFiles"["Image Files"]
        "AudioFiles"["Audio Files"]
        "Text"["Text Content"]
        "Color"["Color Values"]
        "Arrays"["NumPy Arrays"]
    end
    
    subgraph "Clips"
        "VideoFileClip"
        "ImageClip"
        "AudioFileClip"
        "TextClip"
        "ColorClip"
        "BitmapClip"
    end
    
    subgraph "Transform"
        "Resize"["resize"]
        "Rotate"["rotate"]
        "Crop"["crop"]
        "Temporal"["time_transform"]
        "Effects"["with_effects()"]
        "FilterFX"["vfx/afx"]
    end
    
    subgraph "Composite"
        "CompositeVideoClip"
        "CompositeAudioClip"
        "Concatenate"["concatenate_videoclips"]
        "ClipsArray"["clips_array"]
    end
    
    subgraph "Output"
        "VideoOutput"["write_videofile()"]
        "AudioOutput"["write_audiofile()"]
        "GIFOutput"["write_gif()"]
        "ImageSeq"["write_images_sequence()"]
        "Preview"["preview()"]
    end
```

Sources: [README.md:14-42](), [docs/getting_started/quick_presentation.rst:40-73]()

### Example Workflow

Here's a typical MoviePy script workflow:

```python
from moviepy import VideoFileClip, TextClip, CompositeVideoClip

# 1. Create clips
video = VideoFileClip("input.mp4").subclipped(10, 20)
text = TextClip("Hello World", font_size=70, color='white').with_duration(10)

# 2. Apply effects and position
video = video.with_effects([vfx.Resize(width=720)])
text = text.with_position('center')

# 3. Compose clips
final = CompositeVideoClip([video, text])

# 4. Export result
final.write_videofile("output.mp4")
```

Sources: [README.md:15-42]()

## I/O System and FFmpeg Integration

MoviePy uses FFmpeg for reading and writing media files. This architecture enables working with a wide range of media formats:

```mermaid
graph TD
    subgraph "MoviePy Core"
        "VideoClip"
        "AudioClip"
    end
    
    subgraph "I/O Layer"
        "VReader"["FFMPEG_VideoReader"]
        "AReader"["FFMPEG_AudioReader"]
        "VWriter"["FFMPEG_VideoWriter"]
        "AWriter"["FFMPEG_AudioWriter"]
    end
    
    subgraph "Data Representation"
        "VFrame"["NumPy RGB Array"]
        "AFrame"["NumPy Audio Samples"]
    end
    
    subgraph "External Tools"
        "FFmpeg"
        "FFplay"
        "Pillow"
        "NumPy"
    end
    
    "VideoClip" -->|"creates"| "VReader"
    "AudioClip" -->|"creates"| "AReader"
    "VideoClip" -->|"uses"| "VWriter"
    "AudioClip" -->|"uses"| "AWriter"
    
    "VReader" -->|"reads from"| "FFmpeg"
    "AReader" -->|"reads from"| "FFmpeg"
    "VWriter" -->|"writes to"| "FFmpeg"
    "AWriter" -->|"writes to"| "FFmpeg"
    
    "VReader" -->|"produces"| "VFrame"
    "AReader" -->|"produces"| "AFrame"
    
    "VideoClip" -->|"processes"| "VFrame"
    "AudioClip" -->|"processes"| "AFrame"
    
    "VideoClip" -->|"preview"| "FFplay"
    "AudioClip" -->|"preview"| "FFplay"
    
    "VFrame" -->|"based on"| "NumPy"
    "AFrame" -->|"based on"| "NumPy"
    
    "VWriter" -->|"may use"| "Pillow"
```

Sources: [tests/test_VideoClip.py:47-125]()

### Internal Data Representation

- **Video Frames**: Represented as NumPy arrays with shape (height, width, 3) for RGB values
- **Audio Samples**: Represented as NumPy arrays of audio samples
- **Frame Generation**: Each clip has a `frame_function` that generates content for any given timestamp

This NumPy-based representation allows for efficient manipulation of media content in Python.

## Effects and Compositing System

MoviePy provides a rich set of effects through its video effects (vfx) and audio effects (afx) modules:

```mermaid
graph TD
    subgraph "Clips"
        "VideoClip1"["VideoClip"]
        "VideoClip2"["VideoClip"]
        "AudioClip1"["AudioClip"]
    end
    
    subgraph "Effects"
        "VideoEffects"["Video Effects (vfx)"]
        "AudioEffects"["Audio Effects (afx)"]
        
        "VideoEffects" --> "Appearance"["Appearance Effects"]
        "VideoEffects" --> "Spatial"["Spatial Effects"]
        "VideoEffects" --> "Temporal"["Temporal Effects"]
        
        "Appearance" --> "BlackWhite"["BlackAndWhite"]
        "Appearance" --> "InvertColors"["InvertColors"]
        "Appearance" --> "Margin"["Margin"]
        
        "Spatial" --> "Resize"["Resize"]
        "Spatial" --> "Rotate"["Rotate"]
        "Spatial" --> "Crop"["Crop"]
        "Spatial" --> "Mirror"["Mirror"]
        
        "Temporal" --> "FadeIn"["FadeIn"]
        "Temporal" --> "FadeOut"["FadeOut"]
        "Temporal" --> "Loop"["Loop"]
        "Temporal" --> "Freeze"["Freeze"]
        
        "AudioEffects" --> "Volume"["Volume Effects"]
        "AudioEffects" --> "Filter"["Filter Effects"]
        
        "Volume" --> "VolumeX"["MultiplyVolume"]
        "Filter" --> "AudioNormalize"["audio_normalize"]
    end
    
    subgraph "Composition Methods"
        "CompositeVideoClip"
        "CompositeAudioClip"
        "Concatenate"["concatenate_videoclips"]
        "ClipsArray"["clips_array"]
    end
    
    "VideoClip1" -->|"transform"| "VideoEffects"
    "VideoClip2" -->|"transform"| "VideoEffects"
    "AudioClip1" -->|"transform"| "AudioEffects"
    
    "VideoClip1" & "VideoClip2" -->|"overlay"| "CompositeVideoClip"
    "VideoClip1" & "VideoClip2" -->|"sequence"| "Concatenate"
    "VideoClip1" & "VideoClip2" -->|"grid"| "ClipsArray"
    
    "AudioClip1" -->|"mix"| "CompositeAudioClip"
```

Sources: [tests/test_fx.py]()

### Effect Application

Effects are applied using the `with_effects()` method, which takes a list of effect objects:

```python
from moviepy import VideoFileClip, vfx

clip = VideoFileClip("video.mp4")
# Apply multiple effects
new_clip = clip.with_effects([
    vfx.Resize(width=720),
    vfx.BlackAndWhite(),
    vfx.FadeIn(duration=1)
])
```

### Compositing Methods

MoviePy offers several ways to combine clips:

1. **Overlay**: `CompositeVideoClip([background, overlay1, overlay2])` - Places clips on top of each other
2. **Concatenation**: `concatenate_videoclips([clip1, clip2, clip3])` - Places clips sequentially
3. **Grid**: `clips_array([[clip1, clip2], [clip3, clip4]])` - Arranges clips in a grid layout
4. **Audio Mixing**: `CompositeAudioClip([audio1, audio2])` - Mixes audio clips

Sources: [tests/test_VideoClip.py:261-289]()

## API Design Patterns

MoviePy follows several consistent design patterns:

1. **Immutability**: Most operations return a new clip rather than modifying the original
2. **Method Chaining**: Methods return clips, allowing for chained operations
3. **Lazy Evaluation**: Most operations don't process frames until needed
4. **Functional Style**: Effects are functions that transform clips

Example of method chaining:

```python
final_clip = (
    VideoFileClip("video.mp4")
    .subclipped(10, 20)
    .with_effects([vfx.Resize(width=720)])
    .with_audio(
        AudioFileClip("music.mp3")
        .with_effects([afx.MultiplyVolume(0.5)])
    )
)
```

Sources: [moviepy/Clip.py:89-147](), [moviepy/Clip.py:191-207]()

## System Requirements and Dependencies

MoviePy relies on these external libraries:

- **FFmpeg**: For reading and writing media files
- **NumPy**: For internal data representation (arrays)
- **Pillow**: For image processing
- **ImageIO**: For image reading/writing
- **Decorator**: For function decoration utilities
- **Proglog**: For progress logging

MoviePy v2.1.2 requires Python 3.9 or newer.

Sources: [pyproject.toml:29-37](), [CHANGELOG.md]()

## Version History

MoviePy is currently at version 2.1.2 (as of [moviepy/version.py]()), which introduced significant changes from version 1.x. Key improvements in the 2.x series include:

- A new effect system with the `with_effects()` API
- Improved transparency support for video compositing
- Enhanced FFmpeg integration
- Better support for modern Python versions (3.9+)

Sources: [CHANGELOG.md:13-66](), [README.md:6-7]()

## Key System Relationships

This diagram summarizes the relationships between the major components of MoviePy:

```mermaid
graph LR
    "User" --> "MoviePyAPI"["MoviePy API"]
    
    "MoviePyAPI" --> "VideoProcessing"["Video Processing"]
    "MoviePyAPI" --> "AudioProcessing"["Audio Processing"]
    "MoviePyAPI" --> "CompositionSystem"["Composition"]
    "MoviePyAPI" --> "EffectsSystem"["Effects System"]
    
    "VideoProcessing" --> "FFmpegIO"["FFmpeg I/O"]
    "AudioProcessing" --> "FFmpegIO"
    
    "CompositionSystem" --> "VideoProcessing"
    "CompositionSystem" --> "AudioProcessing"
    
    "EffectsSystem" --> "VideoProcessing"
    "EffectsSystem" --> "AudioProcessing"
    
    "FFmpegIO" --> "ExternalTools"["External Tools"]
    
    "ExternalTools" --> "FFmpeg"
    "ExternalTools" --> "Pillow"
    "ExternalTools" --> "NumPy"
    "ExternalTools" --> "ImageIO"
```

Sources: [README.md:44-52](), [docs/getting_started/quick_presentation.rst:48-57]()

## Conclusion

MoviePy provides a powerful, flexible framework for video editing through Python code. Its clip-based architecture, integration with FFmpeg, and rich effects system make it suitable for a wide range of video manipulation tasks. Whether you're automating video workflows, creating dynamic content, or integrating video processing into applications, MoviePy offers a high-level, intuitive API for working with time-based media.