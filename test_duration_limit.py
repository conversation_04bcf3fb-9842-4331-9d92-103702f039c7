#!/usr/bin/env python3
"""
测试t_max_sec参数的一致性应用
验证重构后的时长限制功能是否正确工作
"""

import os
import sys
from pathlib import Path
from enhanced_generator import EnhancedJingwuGenerator, demo_enhanced_features
from lyric_timeline import LyricTimeline, LyricDisplayMode
from lrc_mv_config import load_lrc_mv_config

def test_duration_limit_consistency():
    """测试时长限制的一致性"""
    print("🧪 测试t_max_sec参数的一致性应用")
    print("=" * 60)
    
    # 测试参数
    test_duration = 30.0  # 限制为30秒
    config_path = Path("精武英雄/lrc-mv.yaml")
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        # 加载配置
        print(f"📁 加载配置文件: {config_path}")
        config = load_lrc_mv_config(str(config_path))
        
        # 创建生成器
        generator = EnhancedJingwuGenerator(width=config.width, height=config.height)
        
        # 创建时间轴
        print("🎵 创建歌词时间轴...")
        main_lrc_path = config.get_main_lrc_path()
        main_timeline = LyricTimeline.from_lrc_file(
            str(main_lrc_path),
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW
        )
        
        aux_timeline = None
        if config.aux_lrc:
            aux_lrc_path = config.get_aux_lrc_path()
            aux_timeline = LyricTimeline.from_lrc_file(
                str(aux_lrc_path),
                language="english",
                display_mode=LyricDisplayMode.SIMPLE_FADE
            )
            aux_timeline.set_display_mode(
                LyricDisplayMode.SIMPLE_FADE,
                y_position=config.height // 2 + 200,
                is_highlighted=False
            )
        
        # 显示原始歌词数据统计
        print(f"\n📊 原始歌词数据统计:")
        print(f"   主歌词总行数: {len(main_timeline.lyrics_data)}")
        if main_timeline.lyrics_data:
            print(f"   主歌词时长范围: {main_timeline.lyrics_data[0][0]:.1f}s - {main_timeline.lyrics_data[-1][0]:.1f}s")
        
        if aux_timeline:
            print(f"   副歌词总行数: {len(aux_timeline.lyrics_data)}")
            if aux_timeline.lyrics_data:
                print(f"   副歌词时长范围: {aux_timeline.lyrics_data[0][0]:.1f}s - {aux_timeline.lyrics_data[-1][0]:.1f}s")
        
        # 测试过滤后的歌词数据
        print(f"\n🔍 测试时长限制 ({test_duration}s) 后的歌词过滤:")
        filtered_main = main_timeline.get_filtered_lyrics(test_duration)
        print(f"   主歌词过滤后行数: {len(filtered_main)}")
        if filtered_main:
            print(f"   主歌词过滤后时长范围: {filtered_main[0][0]:.1f}s - {filtered_main[-1][0]:.1f}s")
        
        if aux_timeline:
            filtered_aux = aux_timeline.get_filtered_lyrics(test_duration)
            print(f"   副歌词过滤后行数: {len(filtered_aux)}")
            if filtered_aux:
                print(f"   副歌词过滤后时长范围: {filtered_aux[0][0]:.1f}s - {filtered_aux[-1][0]:.1f}s")
        
        # 测试片段生成
        print(f"\n🎬 测试片段生成 (限制时长: {test_duration}s):")
        main_clips = main_timeline.generate_clips(generator, test_duration)
        print(f"   主歌词生成片段数: {len(main_clips)}")
        
        # 验证主歌词片段时长
        for i, clip in enumerate(main_clips):
            start_time = getattr(clip, 'start', 0)
            duration = getattr(clip, 'duration', 0)
            end_time = start_time + duration
            
            if end_time > test_duration:
                print(f"   ⚠️  主歌词片段 {i} 超出时长限制: {start_time:.2f}s + {duration:.2f}s = {end_time:.2f}s > {test_duration}s")
            else:
                print(f"   ✅ 主歌词片段 {i}: {start_time:.2f}s + {duration:.2f}s = {end_time:.2f}s")
        
        if aux_timeline:
            aux_clips = aux_timeline.generate_clips(generator, test_duration)
            print(f"   副歌词生成片段数: {len(aux_clips)}")
            
            # 验证副歌词片段时长
            for i, clip in enumerate(aux_clips):
                start_time = getattr(clip, 'start', 0)
                duration = getattr(clip, 'duration', 0)
                end_time = start_time + duration
                
                if end_time > test_duration:
                    print(f"   ⚠️  副歌词片段 {i} 超出时长限制: {start_time:.2f}s + {duration:.2f}s = {end_time:.2f}s > {test_duration}s")
                else:
                    print(f"   ✅ 副歌词片段 {i}: {start_time:.2f}s + {duration:.2f}s = {end_time:.2f}s")
        
        # 测试验证方法
        print(f"\n🔧 测试片段验证方法:")
        validated_main = generator._validate_clips_duration(main_clips, test_duration)
        print(f"   主歌词验证前片段数: {len(main_clips)}")
        print(f"   主歌词验证后片段数: {len(validated_main)}")
        
        if aux_timeline:
            validated_aux = generator._validate_clips_duration(aux_clips, test_duration)
            print(f"   副歌词验证前片段数: {len(aux_clips)}")
            print(f"   副歌词验证后片段数: {len(validated_aux)}")
        
        print(f"\n✅ 时长限制一致性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_video_generation():
    """测试完整的视频生成流程"""
    print("\n🎬 测试完整视频生成流程 (30秒限制)")
    print("=" * 60)
    
    config_path = Path("精武英雄/lrc-mv.yaml")
    test_duration = 30.0
    
    # 生成测试视频
    success = demo_enhanced_features(config_path, t_max_sec=test_duration)
    
    if success:
        print("✅ 完整视频生成测试成功")
        
        # 检查输出文件
        config = load_lrc_mv_config(str(config_path))
        output_path = config.get_output_path()
        
        if output_path.exists():
            file_size = output_path.stat().st_size / (1024 * 1024)  # MB
            print(f"📁 输出文件: {output_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 可以添加更多的视频文件验证逻辑
            # 比如使用moviepy读取视频时长等
            
        return True
    else:
        print("❌ 完整视频生成测试失败")
        return False

if __name__ == "__main__":
    print("🚀 开始t_max_sec参数一致性测试")
    print("=" * 80)
    
    # 测试1: 时长限制一致性
    test1_success = test_duration_limit_consistency()
    
    # 测试2: 完整视频生成
    test2_success = test_full_video_generation()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    print(f"   时长限制一致性测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   完整视频生成测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！t_max_sec参数现在一致地应用到所有内容。")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试。")
        sys.exit(1)
