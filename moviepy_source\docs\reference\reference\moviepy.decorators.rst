.. custom module to enable complete documentation of every function
   see https://stackoverflow.com/a/62613202
   
moviepy.decorators
==================


.. automodule:: moviepy.decorators

   

   
   
   


   
   
   .. rubric:: Functions

   .. autosummary::
      :toctree:
   
      add_mask_if_none
      apply_to_audio
      apply_to_mask
      audio_video_effect
      convert_masks_to_RGB
      convert_parameter_to_seconds
      convert_path_to_string
      outplace
      preprocess_args
      requires_duration
      requires_fps
      use_clip_fps_by_default
   
   


   
   
   



