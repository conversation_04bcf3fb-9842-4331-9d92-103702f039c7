# VideoClip

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/video/VideoClip.py](moviepy/video/VideoClip.py)
- [moviepy/video/io/ffmpeg_reader.py](moviepy/video/io/ffmpeg_reader.py)

</details>



The VideoClip class is the central component of MoviePy's video processing system. It serves as the base class for all video objects and provides core functionality for creating, manipulating, and exporting video content. This document explains the purpose, structure, and capabilities of the VideoClip class and its subclasses.

For information about audio processing, see [AudioClip](#2.3).

## 1. Overview

VideoClip is an abstract base class that provides the foundation for all video manipulation in MoviePy. It inherits from the general `Clip` class (see [The Clip System](#2.1)) and adds video-specific functionality including frame processing, visual transformations, masking, and output operations.

```mermaid
classDiagram
    class "Clip" {
        +duration: float
        +start: float
        +end: float
        +transform()
        +time_transform()
        +with_effects()
        +subclipped()
    }
    
    "Clip" <|-- "VideoClip"
    
    class "VideoClip" {
        +frame_function: callable
        +size: tuple
        +mask: VideoClip
        +audio: AudioClip
        +get_frame()
        +write_videofile()
        +preview()
    }
    
    "VideoClip" <|-- "ImageClip"
    "VideoClip" <|-- "ColorClip"
    "VideoClip" <|-- "TextClip"
    "VideoClip" <|-- "BitmapClip"
    "VideoClip" <|-- "VideoFileClip"
    "VideoClip" <|-- "CompositeVideoClip"
    "VideoClip" <|-- "DataVideoClip"
    "VideoClip" <|-- "UpdatedVideoClip"
```

Sources: [moviepy/video/VideoClip.py:45-103](). [moviepy/video/VideoClip.py:1-5]().

## 2. Core Attributes and Properties

### 2.1 Basic Attributes

| Attribute | Type | Description |
|-----------|------|-------------|
| `frame_function` | callable | A function `t -> frame` that returns a video frame at time `t` |
| `size` | tuple | Size of the clip as `(width, height)` in pixels |
| `mask` | VideoClip | Optional mask clip that controls transparency |
| `audio` | AudioClip | Optional audio track attached to the clip |
| `is_mask` | bool | Indicates if the clip is being used as a mask |
| `has_constant_size` | bool | Whether the clip size is constant over time |
| `pos` | callable | A function `t -> (x,y)` defining the clip's position in compositions |
| `relative_pos` | bool | Whether the position is relative or absolute |
| `layer_index` | int | Determines which clip is rendered on top when clips overlap |

### 2.2 Computed Properties

| Property | Returns | Description |
|----------|---------|-------------|
| `w` | int | Width of the video in pixels |
| `h` | int | Height of the video in pixels |
| `aspect_ratio` | float | The width/height ratio |
| `n_frames` | int | Number of frames (requires duration and fps) |

Sources: [moviepy/video/VideoClip.py:105-143]().

## 3. Creating VideoClips

There are multiple ways to create VideoClips depending on the source material. The main constructor takes a frame-generating function, but most users will use the specialized subclasses:

```mermaid
flowchart LR
    subgraph "Creating VideoClips"
        direction TB
        A["VideoClip"] --> B["From Functions"]
        A --> C["From Files"]
        A --> D["From Static Sources"]
        A --> E["From Generated Content"]
        
        B --> B1["Custom frame_function"]
        
        C --> C1["VideoFileClip"]
        C --> C2["ImageSequenceClip"]
        
        D --> D1["ImageClip"]
        D --> D2["ColorClip"]
        
        E --> E1["TextClip"]
        E --> E2["BitmapClip"]
        E --> E3["DataVideoClip"]
    end
```

### 3.1 Direct Initialization

The base VideoClip class can be initialized with:

```python
VideoClip(frame_function=None, is_mask=False, duration=None, has_constant_size=True)
```

Where `frame_function` is a function that takes a time `t` and returns a NumPy array representing an RGB(A) frame.

### 3.2 Specialized Subclasses

The most commonly used subclasses are:

- **ImageClip**: Creates a static clip from an image file or array
- **ColorClip**: Creates a clip of solid color
- **TextClip**: Creates a clip from text
- **VideoFileClip**: Loads a video from a file
- **CompositeVideoClip**: Combines multiple clips

Sources: [moviepy/video/VideoClip.py:105-121](), [moviepy/video/VideoClip.py:1287-1347](), [moviepy/video/VideoClip.py:1404-1445](), [moviepy/video/VideoClip.py:1448-1967]().

## 4. Frame Processing

The core of VideoClip's functionality is its frame processing system, which provides frames on demand for any given time point.

### 4.1 Frame Generation

The central component is the `frame_function` which returns a NumPy array for a given time:

```python
# Example usage:
frame = clip.get_frame(t)  # Returns the frame at time t
```

When FFMPEG is involved for reading video files, the VideoClip uses a reader that efficiently seeks through the video:

```mermaid
flowchart TD
    A["VideoClip.get_frame(t)"] --> B{"Is frame cached?"}
    B -- "Yes" --> C["Return cached frame"]
    B -- "No" --> D["frame_function(t)"]
    
    subgraph "VideoFileClip Implementation"
        D --> E["FFMPEG_VideoReader.get_frame(t)"]
        E --> F{"Does reader need\nrepositioning?"}
        F -- "Yes" --> G["Reinitialize reader"]
        F -- "No" --> H["Read frame\nfrom current position"]
    end
```

Sources: [moviepy/video/VideoClip.py:116](), [moviepy/video/io/ffmpeg_reader.py:234-262]().

## 5. Transformations and Effects

VideoClip provides various methods for transforming clips.

### 5.1 Visual Transformations

| Method | Description |
|--------|-------------|
| `resized(new_size=None, height=None, width=None)` | Resizes the clip to new dimensions |
| `rotated(angle, ...)` | Rotates the clip by the specified angle |
| `cropped(x1=None, y1=None, x2=None, y2=None, ...)` | Crops the clip to the specified region |
| `with_effects(effects, **kwargs)` | Applies one or more effects to the clip |
| `with_effects_on_subclip(effects, start_time, end_time)` | Applies effects to a portion of the clip |

### 5.2 Property Modifications

| Method | Description |
|--------|-------------|
| `with_mask(mask)` | Sets a mask for the clip |
| `without_mask()` | Removes any mask |
| `with_audio(audioclip)` | Attaches an audio track |
| `without_audio()` | Removes any audio |
| `with_opacity(opacity)` | Sets the opacity level |
| `with_position(pos, relative=False)` | Sets position in compositions |
| `with_layer_index(index)` | Sets rendering layer in compositions |

Sources: [moviepy/video/VideoClip.py:643-671](), [moviepy/video/VideoClip.py:926-1020](), [moviepy/video/VideoClip.py:1022-1095]().

## 6. Exporting VideoClips

VideoClip provides multiple methods for exporting content.

### 6.1 Export Methods

| Method | Description |
|--------|-------------|
| `write_videofile(filename, ...)` | Writes the clip as a video file |
| `write_images_sequence(name_format, ...)` | Saves each frame as an image file |
| `write_gif(filename, ...)` | Creates an animated GIF from the clip |
| `save_frame(filename, t=0, ...)` | Saves a single frame at time t |

### 6.2 The Export Pipeline

```mermaid
flowchart TD
    A["VideoClip"] --> B["write_videofile()"]
    A --> C["write_gif()"]
    A --> D["write_images_sequence()"]
    A --> E["save_frame()"]
    
    B --> F["Audio\nProcessing?"]
    F -- "Yes" --> G["Process Audio\n(AudioClip.write_audiofile)"]
    F -- "No" --> H["Skip Audio"]
    G --> I["ffmpeg_write_video()"]
    H --> I
    
    C --> J["write_gif_with_imageio()"]
    
    D --> K["Loop through\ntime points"]
    K --> L["save_frame() for\neach frame"]
    
    E --> M["Get frame at time t"]
    M --> N["Save using imageio"]
```

Sources: [moviepy/video/VideoClip.py:172-203](), [moviepy/video/VideoClip.py:208-413](), [moviepy/video/VideoClip.py:417-469](), [moviepy/video/VideoClip.py:474-525]().

## 7. Compositing

VideoClip provides functionality for compositing multiple clips together.

### 7.1 Compositing Methods

| Method | Description |
|--------|-------------|
| `with_background_color(size=None, color=(0,0,0), pos=None)` | Places clip on a colored background |
| `compose_on(background, t)` | Returns result of clip's frame at time t on a background |
| `compose_mask(background_mask, t)` | Composes clip's mask with a background mask |

### 7.2 Composition Operators

VideoClip overloads several operators for easy composition:

| Operator | Example | Result |
|----------|---------|--------|
| `+` | `clip1 + clip2` | Concatenates clips (placed one after another) |
| `|` | `clip1 | clip2` | Places clips side by side horizontally |
| `/` | `clip1 / clip2` | Places clips one above the other vertically |
| `@` | `clip @ 45` | Rotates clip by 45 degrees |
| `&` | `clip & mask` | Applies mask to clip |

```mermaid
flowchart TB
    subgraph "Composition System"
        A["VideoClip"] --> B["clip1 + clip2\n(concatenate)"]
        A --> C["clip1 | clip2\n(side by side)"]
        A --> D["clip1 / clip2\n(stacked)"]
        A --> E["clip & mask\n(masking)"]
        
        F["CompositeVideoClip"] --> G["Multiple clips\nwith positions"]
        F --> H["Clips with\nz-ordering"]
        
        I["concatenate_videoclips()"] --> J["Sequential\narrangement"]
        
        K["clips_array()"] --> L["Grid\narrangement"]
    end
```

Sources: [moviepy/video/VideoClip.py:685-910](), [moviepy/video/VideoClip.py:1144-1193]().

## 8. Preview and Visualization

VideoClip provides methods for previewing content:

| Method | Description |
|--------|-------------|
| `show(t=0, with_mask=True)` | Displays a single frame at time t |
| `preview(fps=15, audio=True, ...)` | Plays the clip in a window |

Sources: [moviepy/video/VideoClip.py:530-638]().

## 9. Specialized VideoClip Subclasses

Beyond the common subclasses, there are special-purpose VideoClip types:

### 9.1 DataVideoClip

A VideoClip whose frames are functions of successive datasets:

```python
DataVideoClip(data, data_to_frame, fps)
```

Where `data` is a list of datasets and `data_to_frame` converts each dataset to a frame.

### 9.2 UpdatedVideoClip

A VideoClip that updates an internal state between frames:

```python
UpdatedVideoClip(world, is_mask=False, duration=None)
```

Where `world` has methods `update()` and `to_frame()`.

Sources: [moviepy/video/VideoClip.py:1196-1275]().

## 10. Implementation Details

### 10.1 Cloning and Copying

VideoClip implements a `__copy__` method that creates a shallow copy of the clip but performs a deep copy of attributes like `mask` and `audio`.

### 10.2 Resource Management

VideoClip relies on external processes (like FFMPEG) for many operations. For VideoFileClip, resources are managed through the `FFMPEG_VideoReader` class, which handles process creation and termination.

Sources: [moviepy/video/VideoClip.py:145-167](), [moviepy/video/io/ffmpeg_reader.py:277-290]().

## 11. Example Usage

```python
# Basic clip creation and export
from moviepy import VideoFileClip
clip = VideoFileClip("myvideo.mp4").subclipped(100, 120)
clip.write_videofile("my_new_video.mp4")

# Transformations
rotated_clip = clip.rotated(45)
resized_clip = clip.resized(width=320)
cropped_clip = clip.cropped(x1=50, x2=250)

# Composition
from moviepy import TextClip, CompositeVideoClip
text = TextClip("Hello World", font_size=70, color="white")
text = text.with_position(("center", "bottom")).with_duration(5)
final_clip = CompositeVideoClip([clip, text])
```

Sources: [moviepy/video/VideoClip.py:326-334]().