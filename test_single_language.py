#!/usr/bin/env python3
"""
测试单语模式的布局引擎重构
验证LayoutEngine作为默认timeline容器的功能
"""

from pathlib import Path
from enhanced_generator import EnhancedJingwuGenerator
from lyric_timeline import LyricTimeline, LyricDisplayMode

def test_single_language_mode():
    """测试单语模式使用布局引擎"""
    print("测试单语模式布局引擎重构")
    print("=" * 50)
    
    # 创建生成器
    generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)
    generator.default_font_size = 60
    
    # 创建单个时间轴
    main_timeline = LyricTimeline.from_lrc_file(
        str(Path("精武英雄/精武英雄 - 甄子丹.lrc")),
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW
    )
    
    print(f"主时间轴信息: {main_timeline.get_info()}")
    
    # 生成单语视频
    success = generator.generate_bilingual_video(
        main_timeline=main_timeline,
        aux_timeline=None,  # 单语模式
        audio_path=str(Path("精武英雄/精武英雄 - 甄子丹.flac")),
        output_path=str(Path("精武英雄/test_single_language.mp4")),
        background_image=str(Path("精武英雄/bg_v.png")),
        t_max_sec=30.0  # 短时间测试
    )
    
    if success:
        print("\n✅ 单语模式测试成功！")
        output_path = Path("精武英雄/test_single_language.mp4")
        if output_path.exists():
            file_size = output_path.stat().st_size / (1024 * 1024)  # MB
            print(f"输出文件: {output_path}")
            print(f"文件大小: {file_size:.1f} MB")
    else:
        print("\n❌ 单语模式测试失败！")
    
    return success

if __name__ == "__main__":
    test_single_language_mode()
