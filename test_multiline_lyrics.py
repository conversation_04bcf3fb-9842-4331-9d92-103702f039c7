#!/usr/bin/env python3
"""
测试多行歌词支持
验证LRC解析和渲染是否正确处理包含换行的歌词
"""

import os
import tempfile
from pathlib import Path
from lyric_timeline import LyricTimeline, LyricDisplayMode
from enhanced_generator import Enhanced<PERSON>ingwuGenerator

def create_test_lrc_with_multiline():
    """创建包含多行歌词的测试LRC文件"""
    lrc_content = """[ti:Test Multiline]
[ar:Test Artist]
[al:Test Album]
[by:Test]
[offset:0]

[00:00.00]Test Multiline - Test Artist
[00:02.00]
[00:05.00]Single line lyric
[00:08.00]
[00:10.00]First line of multiline
[00:10.00]Second line of multiline
[00:15.00]
[00:18.00]Another single line
[00:22.00]
[00:25.00]Triple line starts here
[00:25.00]This is the second line
[00:25.00]And this is the third line
[00:30.00]
[00:35.00]Final single line
"""

    # 创建临时LRC文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.lrc', delete=False, encoding='utf-8') as f:
        f.write(lrc_content)
        return f.name

def create_test_audio():
    """创建或使用现有的测试音频文件"""
    # 首先检查是否有现有的测试音频
    if os.path.exists("test_audio.wav"):
        print("✅ 使用现有测试音频文件")
        return "test_audio.wav"

    try:
        from moviepy.editor import AudioFileClip
        import numpy as np

        # 生成简单的正弦波音频数据
        sample_rate = 22050
        duration = 40
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio_data = np.sin(2 * np.pi * 440 * t)

        # 使用scipy保存音频（如果可用）
        try:
            from scipy.io.wavfile import write
            audio_path = "test_multiline_audio.wav"
            write(audio_path, sample_rate, (audio_data * 32767).astype(np.int16))
            return audio_path
        except ImportError:
            print("⚠️  scipy不可用，跳过音频创建")
            return None

    except Exception as e:
        print(f"创建测试音频失败: {e}")
        return None

def test_lrc_parsing():
    """测试LRC解析功能"""
    print("🔍 测试LRC解析功能")
    print("=" * 50)

    # 创建测试LRC文件
    lrc_path = create_test_lrc_with_multiline()

    try:
        # 解析LRC文件
        timeline = LyricTimeline.from_lrc_file(lrc_path, language="test")

        print(f"✅ 成功解析LRC文件，共 {len(timeline.lyrics_data)} 条歌词")

        # 显示解析结果
        for i, (timestamp, text) in enumerate(timeline.lyrics_data):
            lines = text.split('\n')
            if len(lines) > 1:
                print(f"📝 [{timestamp:06.2f}s] 多行歌词 ({len(lines)} 行):")
                for j, line in enumerate(lines):
                    print(f"    {j+1}: {line}")
            else:
                print(f"📝 [{timestamp:06.2f}s] 单行: {text}")

        return timeline

    finally:
        # 清理临时文件
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def test_rect_calculation(timeline):
    """测试区域计算"""
    print("\n📐 测试区域计算")
    print("=" * 50)

    video_width, video_height = 720, 1280

    # 测试简单模式
    timeline.set_display_mode(LyricDisplayMode.SIMPLE_FADE)
    simple_rect = timeline.calculate_required_rect(video_width, video_height)
    print(f"✅ 简单模式区域: {simple_rect}")

    # 测试增强预览模式
    timeline.set_display_mode(LyricDisplayMode.ENHANCED_PREVIEW)
    enhanced_rect = timeline.calculate_required_rect(video_width, video_height)
    print(f"✅ 增强模式区域: {enhanced_rect}")

    return simple_rect, enhanced_rect

def test_video_generation(timeline):
    """测试视频生成"""
    print("\n🎬 测试视频生成")
    print("=" * 50)

    # 创建测试音频
    audio_path = create_test_audio()
    if not audio_path:
        print("❌ 无法创建测试音频，跳过视频生成测试")
        return False

    try:
        # 创建生成器
        generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)

        # 设置为简单模式
        timeline.set_display_mode(LyricDisplayMode.SIMPLE_FADE)

        output_path = "test_multiline_output.mp4"

        print("🔧 开始生成多行歌词视频...")

        # 生成视频
        success = generator.generate_bilingual_video(
            main_timeline=timeline,
            aux_timeline=None,
            audio_path=audio_path,
            output_path=output_path,
            background_image=None,
            t_max_sec=40.0
        )

        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"✅ 视频生成成功！")
            print(f"📁 输出文件: {output_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            return True
        else:
            print("❌ 视频生成失败")
            return False

    finally:
        # 清理临时文件
        if audio_path and os.path.exists(audio_path):
            os.unlink(audio_path)

def main():
    """主测试函数"""
    print("🎵 多行歌词支持测试")
    print("=" * 60)

    try:
        # 1. 测试LRC解析
        timeline = test_lrc_parsing()
        if not timeline:
            print("❌ LRC解析测试失败")
            return

        # 2. 测试区域计算
        simple_rect, enhanced_rect = test_rect_calculation(timeline)

        # 3. 测试视频生成
        video_success = test_video_generation(timeline)

        # 总结
        print("\n🎉 测试总结")
        print("=" * 50)
        print("✅ LRC解析: 支持相同时间点多条记录合并")
        print("✅ 区域计算: 支持多行文本高度计算")
        print(f"{'✅' if video_success else '❌'} 视频生成: {'成功' if video_success else '失败'}")

        if video_success:
            print("\n💡 多行歌词支持已完全实现！")
            print("   - 相同时间点的LRC记录会自动合并为多行")
            print("   - 文本渲染支持换行符显示")
            print("   - 布局计算考虑多行文本高度")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
