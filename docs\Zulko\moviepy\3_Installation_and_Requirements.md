# Installation and Requirements

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [docs/getting_started/quick_presentation.rst](docs/getting_started/quick_presentation.rst)
- [docs/index.rst](docs/index.rst)
- [moviepy/version.py](moviepy/version.py)
- [pyproject.toml](pyproject.toml)

</details>



This document covers the installation process for MoviePy, including system requirements, installation methods, dependencies, and troubleshooting tips. For information about getting started with MoviePy after installation, see [Getting Started](#1.1).

## System Requirements

Before installing MoviePy, ensure your system meets the following requirements:

### Python Version Compatibility

MoviePy is compatible with the following Python versions:

- Python 3.9
- Python 3.10
- Python 3.11

### Operating System Compatibility

MoviePy is cross-platform and can run on:

- Windows
- macOS
- Linux

### External Software Requirements

MoviePy relies on FFmpeg for video and audio processing. While the `imageio_ffmpeg` package typically handles this dependency automatically, having FFmpeg installed directly on your system can provide better performance and more flexibility.

```mermaid
flowchart TB
    subgraph "MoviePy External Dependencies"
        FFmpeg["FFmpeg - Essential
        Video/audio processing engine"]
        FFplay["FFplay - Optional
        Media playback for preview"]
    end
    
    subgraph "Python Dependencies"
        NumPy["NumPy - Required
        Array processing"]
        Pillow["Pillow - Required
        Image processing"]
        ImageIO["ImageIO - Required
        Image/video I/O"]
        Others["Other Python dependencies"]
    end
    
    MoviePy --> FFmpeg
    MoviePy --> FFplay
    MoviePy --> NumPy
    MoviePy --> Pillow
    MoviePy --> ImageIO
    MoviePy --> Others
```

Sources: [pyproject.toml:29-37](), [docs/getting_started/quick_presentation.rst:51-53]()

## Installation Methods

### Standard Installation (Using pip)

The simplest way to install MoviePy is through pip:

```bash
pip install moviepy
```

This will install MoviePy and its required dependencies.

### Optional Dependencies

MoviePy has several optional dependency groups for specific purposes:

```mermaid
flowchart TB
    MoviePy["MoviePy Core Package"]
    
    subgraph "Optional Dependency Groups"
        Doc["Documentation
        Sphinx, numpydoc, etc."]
        Test["Testing
        pytest, coverage, etc."]
        Lint["Linting
        black, flake8, etc."]
    end
    
    MoviePy --> Doc
    MoviePy --> Test
    MoviePy --> Lint
```

To install MoviePy with optional dependencies for specific purposes:

```bash
# For documentation tools
pip install moviepy[doc]

# For testing tools
pip install moviepy[test]

# For code linting tools
pip install moviepy[lint]

# Multiple optional dependency groups can be combined
pip install moviepy[test,lint]
```

Sources: [pyproject.toml:39-60]()

### Installing from Source

For the latest development version or to contribute to MoviePy:

```bash
git clone https://github.com/Zulko/moviepy.git
cd moviepy
pip install -e .
```

## Dependencies

### Core Dependencies

MoviePy requires the following Python packages:

| Package | Version | Purpose |
|---------|---------|---------|
| decorator | >=4.0.2,<6.0 | Function decorators |
| imageio | >=2.5,<3.0 | Reading and writing image data |
| imageio_ffmpeg | >=0.2.0 | FFmpeg integration for imageio |
| numpy | >=1.25.0 | Numerical operations on video/audio frames |
| proglog | <=1.0.0 | Progress logging |
| python-dotenv | >=0.10 | Environment variable management |
| pillow | >=9.2.0,<12.0 | Image processing |

Sources: [pyproject.toml:29-37]()

### Dependency Flow in MoviePy

The following diagram illustrates how dependencies are used within MoviePy's architecture:

```mermaid
flowchart TD
    User["User Code"] --> MoviePy["MoviePy API"]
    
    subgraph "Core Components"
        VideoClip["VideoClip"]
        AudioClip["AudioClip"]
        Effects["Effects System"]
        Compositing["Composition"]
    end
    
    subgraph "I/O Systems"
        VideoIO["Video I/O"]
        AudioIO["Audio I/O"]
    end
    
    subgraph "External Dependencies"
        FFmpeg["FFmpeg/imageio_ffmpeg"]
        NumPy["NumPy Arrays"]
        Pillow["Pillow/PIL"]
        ImageIO["ImageIO"]
    end
    
    MoviePy --> VideoClip
    MoviePy --> AudioClip
    MoviePy --> Effects
    MoviePy --> Compositing
    
    VideoClip --> VideoIO
    AudioClip --> AudioIO
    
    VideoIO --> FFmpeg
    AudioIO --> FFmpeg
    
    VideoClip --> NumPy
    AudioClip --> NumPy
    
    VideoIO --> Pillow
    VideoIO --> ImageIO
    
    Effects --> NumPy
    Effects --> Pillow
```

Sources: [docs/getting_started/quick_presentation.rst:51-53]()

## Verifying Your Installation

After installing MoviePy, you can verify the installation by running a simple test:

```python
import moviepy
print(moviepy.__version__)
```

This should print the version of MoviePy you installed (e.g., `2.1.2`).

Sources: [moviepy/version.py:1]()

For a more comprehensive test that also verifies FFmpeg functionality:

```python
from moviepy.editor import VideoFileClip
import tempfile
import os

# Create a temporary text clip
from moviepy.editor import TextClip
clip = TextClip("MoviePy Installation Test", fontsize=70, color='white', bg_color='black')
clip = clip.set_duration(2)

# Write to a temporary file
temp_file = os.path.join(tempfile.gettempdir(), "moviepy_test.mp4")
clip.write_videofile(temp_file, fps=24)

# Try to read it back
test_clip = VideoFileClip(temp_file)
print(f"Clip duration: {test_clip.duration}s")
test_clip.close()

# Clean up
os.remove(temp_file)
print("Installation test successful!")
```

If this runs without errors, your MoviePy installation is working correctly.

## Troubleshooting

### Common Issues

#### FFmpeg Not Found

If you encounter errors related to FFmpeg:

1. Ensure `imageio_ffmpeg` is installed: `pip install imageio_ffmpeg`
2. Alternatively, install FFmpeg directly on your system:
   - **Windows**: Download from [ffmpeg.org](https://ffmpeg.org/download.html) and add to PATH
   - **macOS**: Use Homebrew: `brew install ffmpeg`
   - **Linux**: Use your package manager, e.g., `apt install ffmpeg` or `yum install ffmpeg`

#### Import Errors

If you see import errors, check that all dependencies are installed:

```bash
pip install -U moviepy
```

This will update MoviePy and ensure all dependencies are installed.

#### Version Conflicts

If you experience version conflicts with dependencies:

```bash
pip install -U --force-reinstall moviepy
```

## Environment Variables

MoviePy supports the following environment variables for configuration:

| Variable | Purpose |
|----------|---------|
| FFMPEG_BINARY | Path to the FFmpeg binary if not in standard location |
| IMAGEIO_FFMPEG_EXE | Path to FFmpeg executable for imageio_ffmpeg |
| MOVIEPY_TEMP_DIR | Custom temporary directory for MoviePy operations |

Sources: [pyproject.toml:35]()

## Next Steps

After installing MoviePy, refer to [Getting Started](#1.1) for an introduction to using the library with basic examples.