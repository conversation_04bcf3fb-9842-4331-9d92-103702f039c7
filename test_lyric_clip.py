#!/usr/bin/env python3
"""
LyricClip测试脚本

测试LyricClip的基础功能和性能
"""

import sys
import time
import tempfile
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_engine import LayoutEngine, VerticalStackStrategy
from layout_types import LyricStyle
from lyric_clip import LyricClip, create_lyric_clip
from enhanced_generator import EnhancedJingwuGenerator


def test_lyric_clip_creation():
    """测试LyricClip创建"""
    print("🧪 测试LyricClip创建")
    print("=" * 50)
    
    # 创建测试歌词数据
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词"),
        (9.0, "第四句歌词")
    ]
    
    # 创建时间轴
    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.SIMPLE_FADE,
        style=LyricStyle(font_size=80),
        element_id="test_timeline",
        priority=1
    )
    
    # 创建布局引擎
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
    layout_engine.add_element(timeline)
    
    # 创建LyricClip
    try:
        lyric_clip = create_lyric_clip(
            timelines=[timeline],
            layout_engine=layout_engine,
            size=(720, 1280),
            duration=12.0,
            fps=30
        )
        
        print(f"✅ LyricClip创建成功")
        print(f"   尺寸: {lyric_clip.size}")
        print(f"   时长: {lyric_clip.duration}")
        print(f"   帧率: {lyric_clip.fps}")
        print(f"   时间轴数量: {len(lyric_clip.timelines)}")
        
        return lyric_clip
        
    except Exception as e:
        print(f"❌ LyricClip创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_frame_rendering(lyric_clip: LyricClip):
    """测试帧渲染功能"""
    print("\n🎬 测试帧渲染功能")
    print("=" * 50)
    
    if not lyric_clip:
        print("❌ LyricClip为空，跳过测试")
        return
    
    # 测试不同时间点的帧渲染
    test_times = [0.0, 1.5, 3.0, 4.5, 6.0, 7.5, 9.0, 10.5]
    
    for t in test_times:
        try:
            start_time = time.perf_counter()
            frame = lyric_clip.get_frame(t)
            render_time = time.perf_counter() - start_time
            
            print(f"   时间 {t:4.1f}s: 帧尺寸 {frame.shape}, 渲染耗时 {render_time*1000:.2f}ms")
            
        except Exception as e:
            print(f"   时间 {t:4.1f}s: ❌ 渲染失败 - {e}")


def test_timeline_content_query():
    """测试时间轴内容查询功能"""
    print("\n🔍 测试时间轴内容查询")
    print("=" * 50)
    
    # 创建测试歌词数据
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词"),
        (9.0, "第四句歌词")
    ]
    
    # 创建时间轴
    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.SIMPLE_FADE,
        style=LyricStyle(font_size=80)
    )
    
    # 测试不同时间点的内容查询
    test_times = [0.0, 1.5, 3.0, 4.5, 6.0, 7.5, 9.0, 10.5, 12.0]
    
    for t in test_times:
        content = timeline.get_content_at_time(t)
        if content:
            print(f"   时间 {t:4.1f}s: '{content['text']}' (开始: {content['start_time']:.1f}s, 时长: {content['duration']:.1f}s)")
        else:
            print(f"   时间 {t:4.1f}s: 无歌词内容")


def test_font_cache():
    """测试字体缓存功能"""
    print("\n🔤 测试字体缓存功能")
    print("=" * 50)
    
    from font_cache import FontCache, TextMetricsCache
    
    # 测试字体加载
    try:
        font1 = FontCache.get_font(None, 80, 'chinese')
        font2 = FontCache.get_font(None, 80, 'chinese')  # 应该从缓存获取
        font3 = FontCache.get_font(None, 60, 'chinese')  # 不同大小
        
        print(f"✅ 字体加载成功")
        print(f"   相同字体对象: {font1 is font2}")
        print(f"   不同大小字体: {font1 is font3}")
        
        # 显示缓存信息
        cache_info = FontCache.get_cache_info()
        print(f"   缓存字体数量: {cache_info['cached_fonts']}")
        
    except Exception as e:
        print(f"❌ 字体缓存测试失败: {e}")
    
    # 测试文本测量缓存
    try:
        size1 = TextMetricsCache.get_text_size("测试文本", None, 80, 'chinese')
        size2 = TextMetricsCache.get_text_size("测试文本", None, 80, 'chinese')  # 应该从缓存获取
        
        print(f"✅ 文本测量成功: {size1}")
        
        # 显示缓存信息
        cache_info = TextMetricsCache.get_cache_info()
        print(f"   缓存测量数量: {cache_info['cached_measurements']}")
        
    except Exception as e:
        print(f"❌ 文本测量缓存测试失败: {e}")


def test_performance_comparison():
    """测试性能对比（LyricClip vs 传统方式）"""
    print("\n⚡ 测试性能对比")
    print("=" * 50)
    
    # 创建测试数据
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词"),
        (9.0, "第四句歌词")
    ]
    
    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.SIMPLE_FADE,
        style=LyricStyle(font_size=80)
    )
    
    # 测试LyricClip方式
    print("测试LyricClip方式:")
    try:
        start_time = time.perf_counter()
        
        layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
        layout_engine.add_element(timeline)
        
        lyric_clip = create_lyric_clip(
            timelines=[timeline],
            layout_engine=layout_engine,
            size=(720, 1280),
            duration=12.0,
            fps=30
        )
        
        # 渲染几帧测试
        for t in [0.0, 3.0, 6.0, 9.0]:
            frame = lyric_clip.get_frame(t)
        
        lyric_clip_time = time.perf_counter() - start_time
        print(f"   LyricClip方式耗时: {lyric_clip_time:.3f}秒")
        
    except Exception as e:
        print(f"   ❌ LyricClip方式失败: {e}")
        lyric_clip_time = float('inf')
    
    # 测试传统方式（简化版）
    print("测试传统方式:")
    try:
        start_time = time.perf_counter()
        
        generator = EnhancedJingwuGenerator(720, 1280, 30)
        clips = timeline.generate_clips(generator, 12.0)
        
        traditional_time = time.perf_counter() - start_time
        print(f"   传统方式耗时: {traditional_time:.3f}秒")
        print(f"   生成片段数量: {len(clips)}")
        
    except Exception as e:
        print(f"   ❌ 传统方式失败: {e}")
        traditional_time = float('inf')
    
    # 性能对比
    if lyric_clip_time < float('inf') and traditional_time < float('inf'):
        speedup = traditional_time / lyric_clip_time
        print(f"\n📊 性能对比结果:")
        print(f"   LyricClip: {lyric_clip_time:.3f}秒")
        print(f"   传统方式: {traditional_time:.3f}秒")
        print(f"   性能提升: {speedup:.2f}倍")


def main():
    """主测试函数"""
    print("LyricClip功能测试")
    print("=" * 60)
    
    # 基础功能测试
    lyric_clip = test_lyric_clip_creation()
    test_frame_rendering(lyric_clip)
    test_timeline_content_query()
    test_font_cache()
    test_performance_comparison()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成")


if __name__ == "__main__":
    main()
