#!/usr/bin/env python3
"""
Layout布局器测试脚本

测试新实现的Layout布局器功能，包括：
- LayoutElement接口
- VerticalStackStrategy布局策略
- LayoutEngine布局引擎
- LyricTimeline与布局引擎的集成
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from lyric_timeline import (
    LyricTimeline,
    LyricDisplayMode,
    create_enhanced_timeline,
    create_simple_timeline,
    create_bilingual_timelines
)
from layout_engine import LayoutEngine, VerticalStackStrategy
from layout_types import LyricStyle, LyricRect

def test_layout_element_interface():
    """测试LayoutElement接口实现"""
    print("🧪 测试LayoutElement接口实现")
    print("=" * 50)

    # 创建测试歌词数据
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词"),
        (9.0, "第四句歌词")
    ]

    # 创建时间轴
    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        element_id="test_timeline",
        priority=5,
        is_flexible=True
    )

    # 测试LayoutElement接口
    print(f"✅ Element ID: {timeline.element_id}")
    print(f"✅ Priority: {timeline.priority}")
    print(f"✅ Is Flexible: {timeline.is_flexible}")

    # 测试尺寸计算
    video_width, video_height = 720, 1280
    rect = timeline.calculate_required_rect(video_width, video_height)
    print(f"✅ Required Rect: {rect}")

    # 测试布局属性设置
    timeline.set_layout_properties(priority=3, element_id="updated_timeline")
    print(f"✅ Updated Priority: {timeline.priority}")
    print(f"✅ Updated Element ID: {timeline.element_id}")

    print("✅ LayoutElement接口测试通过！\n")

def test_vertical_stack_strategy():
    """测试垂直堆叠布局策略"""
    print("🧪 测试VerticalStackStrategy布局策略")
    print("=" * 50)

    # 创建测试时间轴
    main_lyrics = [(0.0, "主歌词1"), (3.0, "主歌词2")]
    aux_lyrics = [(0.0, "副歌词1"), (3.0, "副歌词2")]

    main_timeline = create_enhanced_timeline(
        main_lyrics, "chinese", "main_chinese", priority=1
    )
    aux_timeline = create_simple_timeline(
        aux_lyrics, "english", is_highlighted=False,
        element_id="aux_english", priority=10
    )

    # 创建布局策略
    strategy = VerticalStackStrategy(spacing=30)

    # 测试布局计算
    video_width, video_height = 720, 1280
    elements = [main_timeline, aux_timeline]

    layout_result = strategy.arrange_elements(elements, video_width, video_height)

    print("✅ 布局结果:")
    for element_id, rect in layout_result.element_positions.items():
        print(f"  - {element_id}: {rect}")

    print(f"✅ Has Conflicts: {layout_result.has_conflicts}")
    print("✅ VerticalStackStrategy测试通过！\n")

def test_layout_engine():
    """测试布局引擎"""
    print("🧪 测试LayoutEngine布局引擎")
    print("=" * 50)

    # 创建布局引擎
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=40))

    # 创建测试时间轴
    main_lyrics = [(0.0, "主歌词测试"), (3.0, "主歌词测试2")]
    aux_lyrics = [(0.0, "副歌词测试"), (3.0, "副歌词测试2")]

    main_timeline = create_enhanced_timeline(
        main_lyrics, "chinese", "main_test", priority=1
    )
    aux_timeline = create_simple_timeline(
        aux_lyrics, "english", is_highlighted=False,
        element_id="aux_test", priority=5
    )

    # 添加元素到布局引擎
    layout_engine.add_element(main_timeline)
    layout_engine.add_element(aux_timeline)

    print(f"✅ 已添加 {len(layout_engine.elements)} 个元素")

    # 检测冲突
    video_width, video_height = 720, 1280
    conflicts = layout_engine.detect_conflicts(video_width, video_height)

    if conflicts:
        print("⚠️  检测到冲突:")
        for conflict in conflicts:
            print(f"  - {conflict}")
    else:
        print("✅ 无冲突检测")

    # 计算布局
    layout_result = layout_engine.calculate_layout(video_width, video_height)
    print("✅ 最终布局:")
    for element_id, rect in layout_result.element_positions.items():
        print(f"  - {element_id}: {rect}")

    print("✅ LayoutEngine测试通过！\n")

def test_bilingual_layout():
    """测试双语布局"""
    print("🧪 测试双语布局功能")
    print("=" * 50)

    # 创建双语时间轴
    main_lyrics = [
        (0.0, "我是中文主歌词"),
        (3.0, "这是第二句中文"),
        (6.0, "第三句中文歌词")
    ]
    aux_lyrics = [
        (0.0, "I am English subtitle"),
        (3.0, "This is second English line"),
        (6.0, "Third English lyric")
    ]

    main_timeline, aux_timeline = create_bilingual_timelines(
        main_lyrics, aux_lyrics, video_height=1280
    )

    print(f"✅ 主时间轴: {main_timeline.element_id}, 优先级: {main_timeline.priority}")
    print(f"✅ 副时间轴: {aux_timeline.element_id}, 优先级: {aux_timeline.priority}")

    # 测试布局引擎
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=50))
    layout_engine.add_element(main_timeline)
    layout_engine.add_element(aux_timeline)

    # 检测和解决冲突
    video_width, video_height = 720, 1280
    conflicts = layout_engine.detect_conflicts(video_width, video_height)

    if conflicts:
        print("⚠️  检测到布局冲突:")
        for conflict in conflicts:
            print(f"  - {conflict}")

        # 应用自动布局
        layout_result = layout_engine.calculate_layout(video_width, video_height)
        print("✅ 自动布局解决方案:")
        for element_id, rect in layout_result.element_positions.items():
            print(f"  - {element_id}: {rect}")
    else:
        print("✅ 双语布局无冲突")

    print("✅ 双语布局测试通过！\n")

def test_layout_integration():
    """测试布局引擎与视频生成的集成"""
    print("🧪 测试布局引擎集成")
    print("=" * 50)

    # 这里只测试接口，不实际生成视频
    from enhanced_generator import EnhancedJingwuGenerator

    # 创建生成器
    generator = EnhancedJingwuGenerator(width=720, height=1280)

    # 创建测试时间轴
    main_lyrics = [(0.0, "集成测试主歌词"), (3.0, "集成测试主歌词2")]
    aux_lyrics = [(0.0, "Integration test subtitle"), (3.0, "Integration test subtitle 2")]

    main_timeline = create_enhanced_timeline(main_lyrics, "chinese", "integration_main", priority=1)
    aux_timeline = create_simple_timeline(
        aux_lyrics, "english", is_highlighted=False,
        element_id="integration_aux", priority=10
    )

    print(f"✅ 创建生成器: {generator.width}x{generator.height}")
    print(f"✅ 主时间轴信息: {main_timeline.get_info()}")
    print(f"✅ 副时间轴信息: {aux_timeline.get_info()}")

    # 测试布局应用方法
    from lyric_timeline import LyricRect
    test_rect = LyricRect(x=0, y=100, width=720, height=80)

    print("✅ 测试布局应用...")
    generator._apply_layout_to_timeline(aux_timeline, test_rect)
    print("✅ 布局应用成功")

    print("✅ 布局引擎集成测试通过！\n")

def main():
    """运行所有测试"""
    print("🚀 Layout布局器功能测试")
    print("=" * 60)
    print()

    try:
        test_layout_element_interface()
        test_vertical_stack_strategy()
        test_layout_engine()
        test_bilingual_layout()
        test_layout_integration()

        print("🎉 所有测试通过！Layout布局器实现成功！")
        print("💡 现在可以在实际视频生成中使用布局引擎了")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
