@import url(flasky.css)
/* Override some aspects of the pydata-sphinx-theme */

.indexwrapper .sphinxsidebar { visibility: hidden; }

.logo img.logo { width: 120px; height: 120px; padding-right: 30px; }

div.body h1, div.body h2, div.body h3, div.body h4, div.body h5, div.body h6
  { font-family: 'Times New Roman', 'Garamond', 'Georgia', serif; }


:root {
  /* Use softer blue from bootstrap's default info color */
  --pst-color-info: 23, 162, 184;
}

table {
  width: auto; /* Override fit-content which breaks Styler user guide ipynb */
}

/* Main index page overview cards */

.intro-card {
  padding: 30px 10px 20px 10px;
}

.intro-card .sd-card-img-top {
  margin: 10px;
  height: 52px;
  background: none !important;
}

.intro-card .sd-card-title {
  color: var(--pst-color-primary);
  font-size: var(--pst-font-size-h5);
  padding: 1rem 0rem 0.5rem 0rem;
}

.intro-card .sd-card-footer {
  border: none !important;
}

.intro-card .sd-card-footer p.sd-card-text {
  max-width: 220px;
  margin-left: auto;
  margin-right: auto;
}

.intro-card .sd-btn-secondary {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.intro-card .sd-btn-secondary:hover {
  background-color: #5a6268 !important;
  border-color: #545b62 !important;
}

.card, .card img {
  background-color: var(--pst-color-background);
}
