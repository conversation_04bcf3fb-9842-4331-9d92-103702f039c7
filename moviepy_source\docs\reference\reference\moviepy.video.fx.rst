.. custom module to enable complete documentation of every function
   see https://stackoverflow.com/a/62613202
   
moviepy.video.fx
================


.. automodule:: moviepy.video.fx

   

   
   
   


   
   
   


   
   
   



.. rubric:: Modules

.. autosummary::
   :toctree:
   :template: custom_autosummary/module.rst
   :recursive:


   moviepy.video.fx.AccelDecel


   moviepy.video.fx.BlackAndWhite


   moviepy.video.fx.Blink


   moviepy.video.fx.Crop


   moviepy.video.fx.CrossFadeIn


   moviepy.video.fx.CrossFadeOut


   moviepy.video.fx.EvenSize


   moviepy.video.fx.FadeIn


   moviepy.video.fx.FadeOut


   moviepy.video.fx.Freeze


   moviepy.video.fx.FreezeRegion


   moviepy.video.fx.GammaCorrection


   moviepy.video.fx.HeadBlur


   moviepy.video.fx.InvertColors


   moviepy.video.fx.Loop


   moviepy.video.fx.LumContrast


   moviepy.video.fx.MakeLoopable


   moviepy.video.fx.Margin


   moviepy.video.fx.MaskColor


   moviepy.video.fx.MasksAnd


   moviepy.video.fx.MasksOr


   moviepy.video.fx.MirrorX


   moviepy.video.fx.MirrorY


   moviepy.video.fx.MultiplyColor


   moviepy.video.fx.MultiplySpeed


   moviepy.video.fx.Painting


   moviepy.video.fx.Resize


   moviepy.video.fx.Rotate


   moviepy.video.fx.Scroll


   moviepy.video.fx.SlideIn


   moviepy.video.fx.SlideOut


   moviepy.video.fx.SuperSample


   moviepy.video.fx.TimeMirror


   moviepy.video.fx.TimeSymmetrize


