#!/usr/bin/env python3
"""
测试MoviePy 2.2.1升级后的API兼容性
"""

import sys
from pathlib import Path

def test_moviepy_imports():
    """测试MoviePy导入"""
    print("🔍 测试MoviePy导入...")
    try:
        from moviepy import AudioFileClip, ImageClip, CompositeVideoClip, ColorClip
        print("✅ MoviePy导入成功")
        
        # 检查版本
        import moviepy
        print(f"📦 MoviePy版本: {moviepy.__version__}")
        return True
    except ImportError as e:
        print(f"❌ MoviePy导入失败: {e}")
        return False

def test_basic_api():
    """测试基本API调用"""
    print("\n🔍 测试基本API...")
    try:
        from moviepy import ImageClip
        import numpy as np
        
        # 创建一个简单的测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:, :] = [255, 0, 0]  # 红色
        
        # 测试新的API方法
        clip = ImageClip(test_image, duration=1.0)
        clip = clip.with_start(0.5)
        clip = clip.with_position('center')
        
        print("✅ 基本API测试成功")
        print(f"   - 片段时长: {clip.duration}")
        print(f"   - 开始时间: {clip.start}")
        return True
    except Exception as e:
        print(f"❌ 基本API测试失败: {e}")
        return False

def test_enhanced_generator_import():
    """测试enhanced_generator导入"""
    print("\n🔍 测试enhanced_generator导入...")
    try:
        from enhanced_generator import EnhancedJingwuGenerator
        print("✅ EnhancedJingwuGenerator导入成功")
        
        # 创建生成器实例
        generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)
        print(f"✅ 生成器创建成功: {generator.width}x{generator.height}@{generator.fps}fps")
        return True
    except Exception as e:
        print(f"❌ enhanced_generator导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lyric_timeline_import():
    """测试lyric_timeline导入"""
    print("\n🔍 测试lyric_timeline导入...")
    try:
        from lyric_timeline import LyricTimeline, LyricDisplayMode
        print("✅ LyricTimeline导入成功")
        
        # 创建简单的测试时间轴
        test_lyrics = [(0.0, "测试歌词"), (3.0, "第二句")]
        timeline = LyricTimeline(
            lyrics_data=test_lyrics,
            language="chinese",
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )
        print(f"✅ 时间轴创建成功: {timeline.get_info()}")
        return True
    except Exception as e:
        print(f"❌ lyric_timeline导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 MoviePy 2.2.1升级兼容性测试")
    print("=" * 50)
    
    tests = [
        test_moviepy_imports,
        test_basic_api,
        test_enhanced_generator_import,
        test_lyric_timeline_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MoviePy升级成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
