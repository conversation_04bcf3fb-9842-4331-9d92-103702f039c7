---
name: Bug Report
about: Report a bug with MoviePy
title: ''
labels: bug
assignees: ''

---

<!--
You can format code by putting ``` (that's 3 backticks) on a line by itself at the beginning and end of each code block. For example:

```
from moviepy import *
clip = ColorClip((600, 400), color=(255, 100, 0), duration=2)
```

Please, include runnable working example of code that can trigger the bug so we can easily reproduce and investigate the bug.
-->


#### Expected Behavior


#### Actual Behavior


#### Steps and code to Reproduce the Problem
<!-- Please include code that demonstrates this problem so that we can reproduce it. For advice on how to do this, see https://stackoverflow.com/help/mcve

It's higlhy helpfull if you can provide an exact and complete code reproducing the bug, *along with all necessary medias (videos, images, sounds, etc.).* 

Ideally you should provide a functional code snippet that maintainers can run to investigate the bug.
-->


#### Used medias
<!-- If you use any external media in the code triggering the bug, please include them in this issue so we can easily reproduce -->


#### Specifications

  - Python Version:
  - MoviePy Version:
  - Platform Name:
  - Platform Version:
