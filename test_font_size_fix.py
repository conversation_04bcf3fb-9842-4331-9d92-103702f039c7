#!/usr/bin/env python3
"""
测试字体大小修复的脚本
验证极端字体大小设置是否正确生效
"""

from pathlib import Path
from lrc_mv_config import load_lrc_mv_config
from enhanced_generator import demo_enhanced_features
import tempfile
import yaml

def test_extreme_font_sizes():
    """测试极端字体大小设置"""
    print("🔍 测试极端字体大小修复")
    print("=" * 60)
    
    # 创建极端字体大小测试配置
    test_config = {
        'audio': '精武英雄 - 甄子丹.flac',
        'main-lrc': {
            'path': '精武英雄 - 甄子丹.lrc',
            'lang': 'hans',
            'font_size': 15  # 极小字体
        },
        'aux-lrc': {
            'path': 'Jingwu Hero - Donnie Yen.lrc',
            'lang': 'en',
            'font_size': 120  # 极大字体
        },
        'background': 'bg_v.png',
        'width': 720,
        'height': 1280,
        'output': 'test_extreme_font_fix.mp4'
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, 
                                   dir='精武英雄', encoding='utf-8') as f:
        yaml.dump(test_config, f, allow_unicode=True, default_flow_style=False)
        temp_config_path = Path(f.name)
    
    try:
        print(f"📝 创建测试配置文件: {temp_config_path}")
        
        # 加载配置并验证
        config = load_lrc_mv_config(str(temp_config_path))
        
        print(f"✅ 配置加载成功")
        print(f"   主歌词字体大小: {config.main_lrc.font_size}")
        print(f"   副歌词字体大小: {config.aux_lrc.font_size}")
        
        # 验证字体大小
        assert config.main_lrc.font_size == 15, f"主歌词字体大小错误: {config.main_lrc.font_size}"
        assert config.aux_lrc.font_size == 120, f"副歌词字体大小错误: {config.aux_lrc.font_size}"
        
        print("✅ 极端字体大小配置验证通过")
        
        # 生成测试视频（短时间）
        print("\n🎬 生成测试视频...")
        success = demo_enhanced_features(temp_config_path, t_max_sec=15.0)
        
        if success:
            print("✅ 极端字体大小测试视频生成成功！")
            output_path = Path('精武英雄') / 'test_extreme_font_fix.mp4'
            if output_path.exists():
                file_size = output_path.stat().st_size / (1024 * 1024)
                print(f"   输出文件: {output_path}")
                print(f"   文件大小: {file_size:.1f} MB")
                
                # 检查文件是否有合理的大小（说明渲染成功）
                if file_size > 0.1:  # 至少100KB
                    print("✅ 视频文件大小合理，渲染成功")
                    return True
                else:
                    print("❌ 视频文件过小，可能渲染失败")
                    return False
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 测试视频生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        if temp_config_path.exists():
            temp_config_path.unlink()
            print(f"🧹 清理临时配置文件: {temp_config_path}")

def test_normal_font_sizes():
    """测试正常字体大小设置"""
    print("\n🔍 测试正常字体大小")
    print("=" * 60)
    
    # 创建正常字体大小测试配置
    test_config = {
        'audio': '精武英雄 - 甄子丹.flac',
        'main-lrc': {
            'path': '精武英雄 - 甄子丹.lrc',
            'lang': 'hans',
            'font_size': 80  # 正常主歌词字体
        },
        'aux-lrc': {
            'path': 'Jingwu Hero - Donnie Yen.lrc',
            'lang': 'en',
            'font_size': 60  # 正常副歌词字体
        },
        'background': 'bg_v.png',
        'width': 720,
        'height': 1280,
        'output': 'test_normal_font_fix.mp4'
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, 
                                   dir='精武英雄', encoding='utf-8') as f:
        yaml.dump(test_config, f, allow_unicode=True, default_flow_style=False)
        temp_config_path = Path(f.name)
    
    try:
        print(f"📝 创建测试配置文件: {temp_config_path}")
        
        # 加载配置并验证
        config = load_lrc_mv_config(str(temp_config_path))
        
        print(f"✅ 配置加载成功")
        print(f"   主歌词字体大小: {config.main_lrc.font_size}")
        print(f"   副歌词字体大小: {config.aux_lrc.font_size}")
        
        # 验证字体大小
        assert config.main_lrc.font_size == 80, f"主歌词字体大小错误: {config.main_lrc.font_size}"
        assert config.aux_lrc.font_size == 60, f"副歌词字体大小错误: {config.aux_lrc.font_size}"
        
        print("✅ 正常字体大小配置验证通过")
        
        # 生成测试视频（短时间）
        print("\n🎬 生成测试视频...")
        success = demo_enhanced_features(temp_config_path, t_max_sec=15.0)
        
        if success:
            print("✅ 正常字体大小测试视频生成成功！")
            output_path = Path('精武英雄') / 'test_normal_font_fix.mp4'
            if output_path.exists():
                file_size = output_path.stat().st_size / (1024 * 1024)
                print(f"   输出文件: {output_path}")
                print(f"   文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 输出文件不存在")
                return False
        else:
            print("❌ 测试视频生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if temp_config_path.exists():
            temp_config_path.unlink()
            print(f"🧹 清理临时配置文件: {temp_config_path}")

if __name__ == "__main__":
    print("🚀 开始字体大小修复验证测试")
    print("=" * 80)
    
    # 测试1: 极端字体大小
    test1_result = test_extreme_font_sizes()
    
    # 测试2: 正常字体大小
    test2_result = test_normal_font_sizes()
    
    print("\n📊 修复验证测试结果总结")
    print("=" * 80)
    print(f"✅ 极端字体大小测试: {'通过' if test1_result else '失败'}")
    print(f"✅ 正常字体大小测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！字体大小功能修复成功！")
        print("🔧 修复内容:")
        print("   - 在create_lyric_clip_with_animation方法中添加font_size参数")
        print("   - 在SimpleFadeStrategy和EnhancedPreviewStrategy中传递字体大小")
        print("   - 确保字体大小从配置文件正确传递到文本渲染")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
