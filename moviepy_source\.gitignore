# Partially based on
# https://github.com/github/gitignore/blob/main/Python.gitignore

# OS-specific
.DS_Store

# IDEs
.idea
.project
.pydevproject
.sublime-project
.vscode

# Python dev tooling
.mr.developer.cfg
.python-version

# Cache, temp. files
__pycache__/
.cache/
*.py[cod]
*~

# C extensions
*.so

# Distribution / packaging
build/
develop-eggs/
dist/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# System misc.
bin
*.tar.gz

# Installer logs
pip-log.txt

# Unit test / coverage reports
.tox/
.coverage
nosetests.xml

# Media files for testing
tests/media/

# Translations
*.mo

# Documentation
docs/build/

# Publishing
.pypirc
