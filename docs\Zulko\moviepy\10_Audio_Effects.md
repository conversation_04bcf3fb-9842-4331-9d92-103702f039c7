# Audio Effects

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [examples/soundtrack.py](examples/soundtrack.py)
- [moviepy/audio/fx/__init__.py](moviepy/audio/fx/__init__.py)
- [moviepy/decorators.py](moviepy/decorators.py)
- [moviepy/video/fx/__init__.py](moviepy/video/fx/__init__.py)
- [moviepy/video/tools/credits.py](moviepy/video/tools/credits.py)

</details>



## Purpose and Scope

This page documents the audio effects system in MoviePy, explaining how to apply and customize audio transformations for both standalone `AudioClip` objects and the audio components of `VideoClip` objects. For information about video effects, see [Video Effects](#3.1).

Audio effects in MoviePy are designed to modify audio properties such as volume, fades, timing, and normalization. These effects can be applied individually or chained together to create complex audio transformations.

Sources: [moviepy/audio/fx/__init__.py:1-22]()

## Audio Effects Architecture

The audio effects system follows a modular design where each effect is implemented as a separate function or class in the `moviepy/audio/fx/` directory. These effects can be applied to both `AudioClip` objects and the audio tracks of `VideoClip` objects through various methods.

### Audio Effects System Overview

```mermaid
graph TD
    subgraph "Clip Hierarchy"
        Clip["Clip (base class)"]
        Clip --> AudioClip["AudioClip"]
        Clip --> VideoClip["VideoClip"]
        VideoClip -->|"has optional"| Audio["audio (AudioClip)"]
    end
    
    subgraph "Audio Effects System"
        AudioClip -->|"applies"| AudioEffects["Audio Effects (afx)"]
        Audio -->|"applies"| AudioEffects
        
        AudioEffects -->|"contains"| VolumeEffects["Volume Effects"]
        AudioEffects -->|"contains"| TemporalEffects["Temporal Effects"]
        AudioEffects -->|"contains"| FilterEffects["Filter Effects"]
        
        VolumeEffects -->|"implements"| MultiplyVolume["MultiplyVolume"]
        VolumeEffects -->|"implements"| MultiplyStereoVolume["MultiplyStereoVolume"]
        
        TemporalEffects -->|"implements"| AudioFadeIn["AudioFadeIn"]
        TemporalEffects -->|"implements"| AudioFadeOut["AudioFadeOut"]
        TemporalEffects -->|"implements"| AudioLoop["AudioLoop"]
        TemporalEffects -->|"implements"| AudioDelay["AudioDelay"]
        
        FilterEffects -->|"implements"| AudioNormalize["AudioNormalize"]
    end
    
    subgraph "Decorator System"
        Decorator["@audio_video_effect decorator"]
        Decorator -->|"applies audio fx to"| VideoClip
    end
```

Sources: [moviepy/audio/fx/__init__.py:1-22](), [moviepy/decorators.py:65-78]()

## Available Audio Effects

MoviePy includes several built-in audio effects that can be imported from `moviepy.audio.fx`. Each effect serves a specific purpose and can be combined with others for complex audio processing.

### Audio Effects Catalog

| Effect | Category | Purpose | Implementation |
|--------|----------|---------|----------------|
| `MultiplyVolume` | Volume | Changes the volume by a specified factor | Simple scaling of the audio array |
| `MultiplyStereoVolume` | Volume | Applies different volume factors to left and right channels | Scales each channel independently |
| `AudioFadeIn` | Temporal | Gradually increases volume at the beginning | Progressive volume ramping |
| `AudioFadeOut` | Temporal | Gradually decreases volume at the end | Progressive volume ramping |
| `AudioLoop` | Temporal | Repeats audio clip multiple times | Concatenation of the clip with itself |
| `AudioDelay` | Temporal | Adds a delay to the start of audio | Inserts silence at the beginning |
| `AudioNormalize` | Filter | Normalizes audio to a standard volume level | Scales audio to target maximum amplitude |

Sources: [moviepy/audio/fx/__init__.py:1-22]()

## Effect Application Methods

There are multiple ways to apply audio effects to clips in MoviePy, with different approaches suited to different scenarios.

### Application Methods Flow

```mermaid
flowchart TD
    subgraph "Inputs"
        A1["AudioClip"]
        V1["VideoClip with audio"]
    end
    
    subgraph "Application Methods"
        M1["Method 1: Direct Function\nfx.function(clip, *args)"]
        M2["Method 2: with_effects()\nclip.with_effects([effect1, effect2])"]
        M3["Method 3: @audio_video_effect\nDecorator pattern"]
    end
    
    subgraph "Outputs"
        O1["Modified AudioClip"]
        O2["VideoClip with modified audio"]
    end
    
    A1 --> M1 & M2
    V1 --> M1 & M2 & M3
    
    M1 --> O1
    M1 --> O2
    M2 --> O1
    M2 --> O2
    M3 --> O2
```

Sources: [moviepy/decorators.py:65-78]()

### Method 1: Direct Function Application

The most straightforward approach is to call the effect function directly with the clip as the first argument:

```python
from moviepy.editor import AudioFileClip
from moviepy.audio.fx.volumex import volumex

audio = AudioFileClip("audio.mp3")
modified_audio = volumex(audio, 2.0)  # Double the volume
```

### Method 2: The `.with_effects()` Method

The `with_effects()` method allows applying multiple effects in sequence:

```python
from moviepy.editor import AudioFileClip
from moviepy.audio.fx.volumex import volumex
from moviepy.audio.fx.audio_fadein import audio_fadein
from moviepy.audio.fx.audio_fadeout import audio_fadeout

audio = AudioFileClip("audio.mp3")
modified_audio = audio.with_effects([
    lambda clip: volumex(clip, 1.5),
    lambda clip: audio_fadein(clip, 2),
    lambda clip: audio_fadeout(clip, 2)
])
```

### Method 3: Using the `@audio_video_effect` Decorator

The `@audio_video_effect` decorator allows audio effects to be applied directly to video clips:

```python
from moviepy.editor import VideoFileClip
from moviepy.audio.fx.volumex import volumex

video = VideoFileClip("video.mp4")
modified_video = volumex(video, 2.0)  # This only affects the audio component
```

Sources: [moviepy/decorators.py:65-78]()

## Working with Audio in Video Clips

Video clips in MoviePy can have an associated audio track accessible through the `.audio` attribute. Audio effects can be applied directly to this component:

```python
from moviepy.editor import VideoFileClip
from moviepy.audio.fx.volumex import volumex

video = VideoFileClip("video.mp4")
video.audio = volumex(video.audio, 2.0)  # Modify just the audio component
```

The example below shows how to replace a video's audio with an audio file and ensure it has the same duration as the video:

```python
from moviepy.editor import VideoFileClip, AudioFileClip

video = VideoFileClip("video.mp4")
audio = AudioFileClip("music.mp3")

# Ensure audio has the same duration as video
audio = audio.with_duration(video.duration)

# Set the new audio track
video = video.with_audio(audio)
```

Sources: [examples/soundtrack.py:1-20]()

## Implementation Details

### Audio Effects Internal Structure

```mermaid
classDiagram
    class Clip {
        +duration
        +start
        +end
        +with_effects()
    }
    
    class AudioClip {
        +fps
        +to_soundarray()
        +write_audiofile()
    }
    
    class AudioEffect {
        +apply(clip)
    }
    
    Clip <|-- AudioClip
    AudioEffect -- AudioClip : modifies
    
    class MultiplyVolume {
        +factor
        +apply(clip)
    }
    
    class AudioFadeIn {
        +duration
        +apply(clip)
    }
    
    class AudioFadeOut {
        +duration
        +apply(clip)
    }
    
    AudioEffect <|-- MultiplyVolume
    AudioEffect <|-- AudioFadeIn
    AudioEffect <|-- AudioFadeOut
```

Under the hood, audio effects in MoviePy typically manipulate the numerical representation of audio data, obtained through the `to_soundarray()` method of `AudioClip`. This produces a NumPy array that can be modified and then used to create a new `AudioClip`.

The `@audio_video_effect` decorator is key for allowing effects to work on both audio and video clips. It examines whether the input clip has an audio attribute, and if so, applies the effect to that attribute while leaving the video component unchanged.

Sources: [moviepy/decorators.py:65-78](), [moviepy/audio/fx/__init__.py:1-22]()

## Creating Custom Audio Effects

Custom audio effects can be created by defining functions that take an `AudioClip` as input and return a modified `AudioClip`. These custom functions can then be used like the built-in effects.

A custom audio effect typically follows this pattern:

```python
def custom_audio_effect(clip, param1, param2, ...):
    """
    Documentation for the custom effect
    """
    # Get the audio as a numpy array
    audio_array = clip.to_soundarray()
    
    # Apply transformations to the array
    modified_array = transform_function(audio_array, param1, param2, ...)
    
    # Create a new AudioClip with the modified array
    new_clip = clip.with_array(modified_array)
    
    return new_clip
```

You can then apply the custom effect using any of the methods described earlier:

```python
modified_audio = custom_audio_effect(audio_clip, param1, param2)
# or
modified_audio = audio_clip.with_effects([
    lambda clip: custom_audio_effect(clip, param1, param2)
])
```

Sources: [moviepy/audio/fx/__init__.py:1-22](), [moviepy/decorators.py:65-78]()