.. _compositing:

Compositing multiple clips
=========================================

Video composition, also known as non-linear editing, is the fact of mixing and playing several clips together in a new clip. This video is a good example of what compositing you can do with MoviePy:

.. raw:: html

        <div style="position: relative; padding-bottom: 56.25%; padding-top: 30px; margin-bottom:30px; height: 0; overflow: hidden; margin-left:15%;">
            <iframe type="text/html" src="https://youtube.com/embed/rIehsqqYFEM?rel=0" frameborder="0"
            style="position: absolute; top: 0; bottom: 10; width: 70%; height: 100%; "></iframe>
        </div>

.. note::
    Before starting, note that video clips generally carry an audio track and a mask, which are also clips. When you compose these clips together, the soundtrack and mask of the final clip are automatically generated by putting together the soundtracks and masks of the clips.
    So most of the time you don't need to worry about mixing the audio and masks.


Juxtaposing and concatenating clips
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Two simple ways of putting clips together is to concatenate them (to play them one after the other in a single long clip) or to juxtapose them (to put them side by side in a single larger clip).

Concatenating multiple clips
"""""""""""""""""""""""""""""""""

Concatenation can be done very easily with the function :py:func:`~moviepy.video.compositing.CompositeVideoClip.concatenate_videoclips`.

.. literalinclude:: /_static/code/user_guide/compositing/concatenate.py
    :language: python

The ``final_clip`` is a clip that plays the clips 1, 2, and 3 one after the other.

.. note::
    The clips do not need to be the same size. If they aren't, they will all appear centered in a clip large enough to contain the biggest of them, with optionally a color of your choosing to fill the background. 

For more info, see :py:func:`~moviepy.video.compositing.CompositeVideoClip.concatenate_videoclips`.


Juxtaposing multiple clips
""""""""""""""""""""""""""""""

Putting multiple clip side by side is done with :py:func:`~moviepy.video.compositing.CompositeVideoClip.clip_array`:

.. literalinclude:: /_static/code/user_guide/compositing/juxtaposing.py
    :language: python

You obtain a clip which looks like this:

.. figure:: /_static/medias/user_guide/stacked.jpeg
   :align: center

For more info, see :py:func:`~moviepy.video.compositing.CompositeVideoClip.clip_array`.


More complex video compositing 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The :py:class:`~moviepy.video.compositing.CompositeVideoClip.CompositeVideoClip` class is the base of all video compositing. 
For example, internally, both :py:func:`~moviepy.video.compositing.CompositeVideoClip.concatenate_videoclips` and :py:func:`~moviepy.video.compositing.CompositeVideoClip.clip_array` create a :py:class:`~moviepy.video.compositing.CompositeVideoClip.CompositeVideoClip`.

It provides a very flexible way to compose clips, by playing multiple clip *on top of* of each other, in the order they have been passed to :py:class:`~moviepy.video.compositing.CompositeVideoClip.CompositeVideoClip`, here's an example :

.. literalinclude:: /_static/code/user_guide/compositing/CompositeVideoClip.py
    :language: python

Now ``final_clip`` plays all clips at the same time, with ``clip3`` over ``clip2`` over ``clip1``. It means that, if all clips have the same size, then only ``clip3``, which is on top, will be visible in the video... 
Unless  ``clip3`` and/or ``clip2`` have masks which hide parts of them. 

.. note::
    Note that by default the composition has the size of its first clip (as it is generally a *background*). But sometimes you will want to make your clips *float* in a bigger composition.
    To do so, just pass the size of the final composition as ``size`` parameter of :py:class:`~moviepy.video.compositing.CompositeVideoClip.CompositeVideoClip`.

For now we have stacked multiple clip on top of each others, but this is obviously not enough for doing real video compositing. 
For that, we will need to change when some clip starts and stops playing, as well as define the x:y, position of these clips in the final video.

For more info, see :py:class:`~moviepy.video.compositing.CompositeVideoClip.CompositeVideoClip`.

Changing starting and stopping times of clips
""""""""""""""""""""""""""""""""""""""""""""""""

In a CompositionClip, each clip start to play at a time that is specified by his ``clip.start`` attribute, and will play until ``clip.end``.

So, considering that you would want to play ``clip1`` for the first 6 seconds, ``clip2`` 5 seconds after the start of the video, and finally ``clip3`` at the end of ``clip2``, you would do as follows:

.. literalinclude:: /_static/code/user_guide/compositing/with_start.py
    :language: python

.. note::
    When working with timing of your clip, you will frequently want to keep only parts of the original clip.
    To do so, you should take a look at :py:meth:`~moviepy.Clip.Clip.subclipped` and :py:meth:`~moviepy.Clip.Clip.with_section_cut_out`.


Positioning clips
""""""""""""""""""

Frequently, you will want a smaller clip to appear on top of a larger one, and decide where it will appear in the composition by setting their position. 

You can do so by using the :py:meth:`~moviepy.video.VideoClip.VideoClip.with_position` method. The position is always defined from the top left corner, but you can define it
in many ways :

.. literalinclude:: /_static/code/user_guide/compositing/with_position.py
    :language: python

When indicating the position keep in mind that the ``y`` coordinate has its zero at the top of the picture:

.. figure:: /_static/medias/user_guide/videoWH.jpeg


Adding transitions effects
""""""""""""""""""""""""""

The last part of composition is adding transition effects. For example, when a clip start while another is still playing, it would be nice to make the new one fade-in instead of showing abruptly.

To do so, we can use the transitions offered by MoviePy in :py:mod:`~moviepy.video.compositing.transitions`, like :py:func:`~moviepy.video.compositing.transitions.crossfadein` :

.. literalinclude:: /_static/code/user_guide/compositing/crossfadein.py
    :language: python


MoviePy offer only few transitions in :py:mod:`~moviepy.video.compositing.transitions`. But technically, transitions are mostly effects applied to the mask of a clip!
That means you can actually use any of the already existing effects, and use them as transitions by applying them on the mask of your clip (see .

For more info, see :py:mod:`~moviepy.video.compositing.transitions` and :py:mod:`moviepy.video.fx`.


Compositing audio clips
-------------------------

When you mix video clips together, MoviePy will automatically compose their respective audio tracks to form the audio track of the final clip, so you don't need to worry about compositing these tracks yourself.

If you want to make a custom audio track from several audio sources, audio clips can be mixed together like video clips, with :py:class:`~moviepy.audio.AudioClip.CompositeAudioClip` and :py:func:`~moviepy.audio.AudioClip.concatenate_audioclips`:

.. literalinclude:: /_static/code/user_guide/compositing/CompositeAudioClip.py
    :language: python

