# Reading Media

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/video/VideoClip.py](moviepy/video/VideoClip.py)
- [moviepy/video/io/ffmpeg_reader.py](moviepy/video/io/ffmpeg_reader.py)

</details>



This document explains how MoviePy reads different types of media files, including videos, images, and audio. It details the underlying architecture, supported formats, and practical implementation of the media reading subsystem. For information about writing media to files, see [Writing Media](#4.3).

## Overview

MoviePy's media reading system enables developers to open and process various media formats through a unified interface. The system is built on top of FFmpeg, providing cross-platform compatibility and support for numerous formats. Reading media is a fundamental operation that forms the foundation for all subsequent transformations and effects in MoviePy.

```mermaid
flowchart TB
    Input["Media File"] --> FFmpeg["FFmpeg Process"]
    FFmpeg --> Reader["MoviePy Reader Classes"]
    Reader --> Clip["Clip Objects"]
    Clip --> User["User Code"]
    
    subgraph "Reader Layer"
        FFMPEG_VideoReader["FFMPEG_VideoReader"]
        FFMPEG_AudioReader["FFMPEG_AudioReader"]
        ImageReader["Image Reading (imread)"]
    end
    
    subgraph "Clip Layer"
        VideoFileClip["VideoFileClip"]
        AudioFileClip["AudioFileClip"]
        ImageClip["ImageClip"]
    end
    
    Reader --> Reader_Layer
    Clip --> Clip_Layer
```

Sources: [moviepy/video/io/ffmpeg_reader.py:18-91](). [moviepy/video/VideoClip.py:1288-1347]()

## Core Reading Components

### FFMPEG_VideoReader

The `FFMPEG_VideoReader` class handles the low-level interaction with FFmpeg for reading video data. It manages a subprocess connection to FFmpeg that streams video frames on demand.

Key features include:
- Frame-accurate seeking within a video file
- Buffering to improve read performance
- Extraction of video metadata (fps, resolution, duration)
- Conversion of video frames to numpy arrays

```mermaid
classDiagram
    class FFMPEG_VideoReader {
        +filename: str
        +fps: float
        +size: tuple
        +duration: float
        +proc: subprocess
        +last_read: numpy.ndarray
        +initialize(start_time)
        +read_frame()
        +get_frame(t)
        +skip_frames(n)
        +close()
    }
```

Sources: [moviepy/video/io/ffmpeg_reader.py:18-290]()

### Reading Video Frames

When a video frame is requested at a specific time (`t`), MoviePy employs an optimization strategy:

1. Convert the time to a frame number based on FPS
2. Check if the requested frame is close to the current reader position
3. If close, read frames sequentially until reaching the target
4. If distant, reinitialize the reader at the target position
5. Return the frame as a numpy array

This approach balances accuracy with performance by avoiding costly seek operations when possible.

```mermaid
flowchart TD
    A["get_frame(t)"] --> B{"Is reader\ninitialized?"}
    B -->|"No"| C["Initialize at time t"]
    B -->|"Yes"| D{"Is frame at\ncurrent position?"}
    D -->|"Yes"| E["Return cached frame"]
    D -->|"No"| F{"Is frame nearby?"}
    F -->|"Yes"| G["Skip to target frame"]
    F -->|"No"| H["Reinitialize at time t"]
    G --> I["Read frame"]
    H --> I
    C --> I
    I --> J["Return frame"]
    E --> J
```

Sources: [moviepy/video/io/ffmpeg_reader.py:234-262]()

### FFmpeg Information Parsing

The `ffmpeg_parse_infos` function and `FFmpegInfosParser` class extract metadata from media files using FFmpeg:

```python
# Example of data returned by ffmpeg_parse_infos
{
    "duration": 120.5,            # Duration in seconds
    "video_found": True,          # Whether video stream exists
    "video_fps": 30.0,            # Frames per second
    "video_size": [1920, 1080],   # Width, height
    "video_duration": 120.49,     # Video stream duration
    "video_n_frames": 3615,       # Calculated number of frames
    "video_bitrate": 5000,        # In kb/s
    "audio_found": True,          # Whether audio stream exists
    "audio_fps": 44100,           # Audio sample rate
    "audio_bitrate": 192,         # In kb/s
    "metadata": {...}             # File metadata
}
```

Sources: [moviepy/video/io/ffmpeg_reader.py:328-654](), [moviepy/video/io/ffmpeg_reader.py:821-910]()

### Image Reading

Reading images is handled primarily through the `ImageClip` class, which can load:
- Image files (PNG, JPEG, etc.) through the `imread_v2` function
- Numpy arrays directly

The `ffmpeg_read_image` function is a lower-level utility that uses FFmpeg to read an image file into a numpy array.

Sources: [moviepy/video/VideoClip.py:1288-1347](), [moviepy/video/io/ffmpeg_reader.py:293-325]()

## Media Reading Workflow

### Creating a VideoFileClip

When you create a `VideoFileClip`, several steps occur behind the scenes:

1. FFmpeg extracts metadata about the file
2. An `FFMPEG_VideoReader` instance is created
3. The reader initializes and positions at the start of the file
4. The first frame is pre-read and cached

```mermaid
sequenceDiagram
    participant User as "User Code"
    participant VFC as "VideoFileClip"
    participant Reader as "FFMPEG_VideoReader"
    participant Parser as "FFmpegInfosParser"
    participant FFmpeg as "FFmpeg Process"
    
    User->>VFC: create VideoFileClip(filename)
    VFC->>Reader: create reader(filename)
    Reader->>Parser: ffmpeg_parse_infos(filename)
    Parser->>FFmpeg: execute ffmpeg -i command
    FFmpeg-->>Parser: return output
    Parser->>Parser: parse output
    Parser-->>Reader: return metadata
    Reader->>FFmpeg: start subprocess for reading
    Reader->>Reader: read first frame
    Reader-->>VFC: ready for reading
    VFC-->>User: clip ready
```

Sources: [moviepy/video/io/ffmpeg_reader.py:21-81]()

### Getting Frames from VideoFileClip

```mermaid
sequenceDiagram
    participant User as "User Code"
    participant Clip as "VideoClip"
    participant Reader as "FFMPEG_VideoReader"
    participant FFmpeg as "FFmpeg Process"
    
    User->>Clip: get_frame(t)
    Clip->>Reader: get_frame(t)
    alt Frame at current position
        Reader-->>Clip: return cached frame
    else Frame nearby
        Reader->>Reader: skip_frames()
        Reader->>FFmpeg: read data
        FFmpeg-->>Reader: frame data
        Reader->>Reader: convert to numpy array
        Reader-->>Clip: return frame
    else Frame far away
        Reader->>Reader: initialize(t)
        Reader->>FFmpeg: seek to position
        FFmpeg-->>Reader: frame data
        Reader->>Reader: convert to numpy array
        Reader-->>Clip: return frame
    end
    Clip-->>User: processed frame
```

Sources: [moviepy/video/io/ffmpeg_reader.py:234-262](), [moviepy/video/io/ffmpeg_reader.py:178-232]()

## Media Reading Class Hierarchy

MoviePy organizes media reading through a hierarchy of classes that provide different levels of abstraction:

```mermaid
classDiagram
    class Clip {
        +duration: float
        +start: float
        +end: float
        +get_frame(t)
    }
    
    class VideoClip {
        +size: tuple
        +fps: float
        +get_frame(t)
        +save_frame(filename, t)
    }
    
    class AudioClip {
        +fps: float
        +get_frame(t)
    }
    
    class VideoFileClip {
        -reader: FFMPEG_VideoReader
    }
    
    class ImageClip {
        +img: numpy.ndarray
    }
    
    class AudioFileClip {
        -reader: FFMPEG_AudioReader
    }
    
    class FFMPEG_VideoReader {
        +filename: str
        +size: tuple
        +fps: float
        +duration: float
        +read_frame()
        +get_frame(t)
    }
    
    Clip <|-- VideoClip
    Clip <|-- AudioClip
    VideoClip <|-- VideoFileClip
    VideoClip <|-- ImageClip
    AudioClip <|-- AudioFileClip
    VideoFileClip --> FFMPEG_VideoReader: uses
    AudioFileClip --> FFMPEG_AudioReader: uses
```

Sources: [moviepy/video/VideoClip.py:45-105](), [moviepy/video/io/ffmpeg_reader.py:18-290]()

## Supported Formats

MoviePy leverages FFmpeg's broad format support. The actual formats available depend on your FFmpeg build, but typically include:

| Category | Common Formats |
|----------|---------------|
| Video    | MP4, AVI, MOV, MKV, WebM, WMV, FLV |
| Image    | PNG, JPEG, BMP, TIFF, GIF |
| Audio    | MP3, WAV, AAC, FLAC, OGG |

## Optimizing Media Reading

### Performance Tips

1. **Sequential Access**: When possible, process frames in sequential order to avoid costly seeking operations.

2. **Resource Management**: Always close readers when finished to release system resources:
   ```python
   clip = VideoFileClip("video.mp4")
   # use clip...
   clip.close()
   ```

3. **Target Resolution**: Use the `target_resolution` parameter to downsample video when reading if you don't need full resolution:
   ```python
   # Load video at half resolution
   clip = VideoFileClip("video.mp4", target_resolution=(960, 540))
   ```

4. **Frame Caching**: For clips you access repeatedly, consider creating a custom caching mechanism.

Sources: [moviepy/video/io/ffmpeg_reader.py:53-62]()

### Common Issues

1. **Memory Usage**: Reading high-resolution videos can consume significant memory, especially when processing many frames.

2. **Seeking Overhead**: Frequent random seeking in a video file is slow. Try to structure your code to process frames sequentially.

3. **FFmpeg Compatibility**: Different FFmpeg versions may have slight differences in behavior. MoviePy is generally tested with recent FFmpeg releases.

## Technical Implementation Details

### FFmpeg Subprocess Communication

MoviePy communicates with FFmpeg through pipes, sending commands and receiving frame data as binary streams. The raw frame data is then converted to numpy arrays for further processing.

Example FFmpeg command used for reading:
```
ffmpeg -ss [start_time] -i [filename] -f image2pipe -vf scale=[width]:[height] 
-sws_flags bicubic -pix_fmt rgb24 -vcodec rawvideo -
```

This command instructs FFmpeg to:
- Seek to a specific start time
- Open the input file
- Output frames as a pipe
- Scale to the desired resolution
- Use a specific scaling algorithm
- Set the pixel format
- Output raw video data

Sources: [moviepy/video/io/ffmpeg_reader.py:125-156]()

### Frame Reading Process

The actual reading of frames happens in the `read_frame()` method:

1. Calculate the number of bytes needed for one frame
2. Read that many bytes from the FFmpeg stdout pipe
3. Check if the correct number of bytes was read
4. Convert the bytes to a numpy array
5. Reshape the array to match the frame dimensions
6. Cache the frame for potential future use

Sources: [moviepy/video/io/ffmpeg_reader.py:178-232]()

## Summary

MoviePy's media reading system provides a powerful yet simple interface for accessing and manipulating media content. By leveraging FFmpeg, it offers broad format support and reliable performance across platforms. Understanding the reading system is essential for efficiently working with MoviePy, especially for applications that process large media files or require frame-accurate operations.