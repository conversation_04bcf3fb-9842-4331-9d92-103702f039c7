# Video Effects

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [.github/workflows/formatting_linting.yml](.github/workflows/formatting_linting.yml)
- [examples/soundtrack.py](examples/soundtrack.py)
- [moviepy/decorators.py](moviepy/decorators.py)
- [moviepy/video/fx/Blink.py](moviepy/video/fx/Blink.py)
- [moviepy/video/fx/Crop.py](moviepy/video/fx/Crop.py)
- [moviepy/video/fx/FreezeRegion.py](moviepy/video/fx/FreezeRegion.py)
- [moviepy/video/fx/MakeLoopable.py](moviepy/video/fx/MakeLoopable.py)
- [moviepy/video/fx/Margin.py](moviepy/video/fx/Margin.py)
- [moviepy/video/fx/Resize.py](moviepy/video/fx/Resize.py)
- [moviepy/video/fx/Rotate.py](moviepy/video/fx/Rotate.py)
- [moviepy/video/io/ffplay_previewer.py](moviepy/video/io/ffplay_previewer.py)
- [moviepy/video/tools/credits.py](moviepy/video/tools/credits.py)
- [setup.cfg](setup.cfg)

</details>



This document details the video effects system in MoviePy and the available effects for transforming video clips. Video effects enable transformations like resizing, rotating, cropping, and applying visual modifications to video content. For information about audio effects, see [Audio Effects](#3.2).

## Effect Architecture

MoviePy implements video effects through a robust object-oriented system where each effect extends the base `Effect` class. Effects are applied to `VideoClip` objects using the `with_effects()` method, which creates a new clip with the applied effects without modifying the original.

```mermaid
classDiagram
    class Effect {
        +apply(clip)
    }
    
    class VideoClip {
        +with_effects(effects_list)
        +transform()
        +image_transform()
    }
    
    Effect <|-- SpatialEffect
    Effect <|-- TemporalEffect
    Effect <|-- RegionEffect
    
    SpatialEffect <|-- Resize
    SpatialEffect <|-- Rotate
    SpatialEffect <|-- Crop
    SpatialEffect <|-- Margin
    
    TemporalEffect <|-- MakeLoopable
    TemporalEffect <|-- Blink
    
    RegionEffect <|-- FreezeRegion
    
    VideoClip --> Effect : "applies"
```

**Effect Application Process**

Sources: [moviepy/video/fx/Resize.py:55-158](), [moviepy/Effect.py]()

### Effect Implementation Pattern

Effects in MoviePy follow a consistent implementation pattern using Python's dataclasses:

1. Each effect is implemented as a class that extends the base `Effect` class
2. Effect parameters are defined as dataclass fields
3. An `apply()` method implements the transformation logic
4. The `apply()` method returns a new transformed clip

```mermaid
flowchart TD
    A["User Code"] --> B["clip.with_effects([effect1, effect2, ...])"]
    B --> C["Create copy of original clip"]
    C --> D["For each effect in list"]
    D --> E["Call effect.apply(clip)"]
    E --> F["Return transformed clip"]
    D --> G["Apply next effect"]
    G --> E
    F --> H["Return final transformed clip"]
```

**Video Effect Application Workflow**

Sources: [moviepy/decorators.py:12-16](), [moviepy/video/fx/Resize.py:55-158]()

## Available Video Effects

MoviePy provides several categories of video effects for different transformation needs:

### Spatial Effects

Spatial effects modify the dimensions, orientation, or visible region of a video clip.

| Effect | Description | Key Parameters |
|--------|-------------|----------------|
| Resize | Changes the dimensions of a clip | `new_size`, `width`, `height` |
| Rotate | Rotates a clip by specified angle | `angle`, `unit`, `expand` |
| Crop | Extracts a rectangular region from a clip | `x1`, `y1`, `x2`, `y2`, `width`, `height` |
| Margin | Adds a margin around the clip | `margin_size`, `left`, `right`, `top`, `bottom`, `color` |

#### Resize

The `Resize` effect changes the dimensions of a clip while maintaining aspect ratio unless specific dimensions are provided.

```python
# Resize to specific dimensions
clip.with_effects([Resize((460, 720))])

# Scale by factor
clip.with_effects([Resize(0.5)])  # Half the original size

# Resize by width only (height calculated automatically)
clip.with_effects([Resize(width=800)])

# Dynamic resize (function of time)
clip.with_effects([Resize(lambda t: 1 + 0.02*t)])  # Gradually enlarges
```

Sources: [moviepy/video/fx/Resize.py:11-158]()

#### Rotate

The `Rotate` effect rotates a clip by a specified angle.

```python
# Rotate by 45 degrees
clip.with_effects([Rotate(45)])

# Rotate by π/4 radians
clip.with_effects([Rotate(math.pi/4, unit="rad")])

# Rotate with transparent background
clip.with_mask().with_effects([Rotate(72)])
```

Sources: [moviepy/video/fx/Rotate.py:11-128]()

#### Crop

The `Crop` effect extracts a rectangular region from a clip.

```python
# Crop with explicit coordinates
clip.with_effects([Crop(x1=50, y1=60, x2=460, y2=275)])

# Remove portion above y=30
clip.with_effects([Crop(y1=30)])

# Crop with width and position
clip.with_effects([Crop(x_center=300, width=400, y1=100, y2=600)])
```

Sources: [moviepy/video/fx/Crop.py:7-81]()

#### Margin

The `Margin` effect adds borders around a clip.

```python
# Add 20px margin all around
clip.with_effects([Margin(margin_size=20)])

# Add different margins on each side
clip.with_effects([Margin(left=10, right=20, top=15, bottom=25)])

# Add red transparent margin
clip.with_effects([Margin(margin_size=30, color=(255, 0, 0), opacity=0.5)])
```

Sources: [moviepy/video/fx/Margin.py:10-91]()

### Temporal Effects

Temporal effects modify how the clip plays over time.

| Effect | Description | Key Parameters |
|--------|-------------|----------------|
| MakeLoopable | Creates a seamless loop | `overlap_duration` |
| Blink | Makes the clip blink on and off | `duration_on`, `duration_off` |

#### MakeLoopable

The `MakeLoopable` effect creates a seamless looping clip by fading the beginning of the clip back in at its end.

```python
# Make a clip loop with 1 second crossfade
clip.with_effects([MakeLoopable(1.0)])
```

Sources: [moviepy/video/fx/MakeLoopable.py:9-31]()

#### Blink

The `Blink` effect makes a clip alternate between visible and invisible.

```python
# Blink on for 0.5 seconds, off for 0.3 seconds
clip.with_effects([Blink(duration_on=0.5, duration_off=0.3)])
```

Sources: [moviepy/video/fx/Blink.py:6-28]()

### Region-Based Effects

Region effects modify specific parts of a frame while leaving other parts unchanged.

| Effect | Description | Key Parameters |
|--------|-------------|----------------|
| FreezeRegion | Freezes a region while the rest animates | `t`, `region`, `outside_region`, `mask` |

#### FreezeRegion

The `FreezeRegion` effect freezes a specific region of the clip at a given time while the rest of the clip continues playing.

```python
# Freeze a specific region defined by coordinates
clip.with_effects([FreezeRegion(t=2.0, region=(100, 100, 300, 300))])

# Freeze everything except a specific region
clip.with_effects([FreezeRegion(t=2.0, outside_region=(100, 100, 300, 300))])

# Freeze a region defined by a mask
mask = some_mask_clip  # A mask clip defining the region
clip.with_effects([FreezeRegion(t=2.0, mask=mask)])
```

Sources: [moviepy/video/fx/FreezeRegion.py:9-69]()

## Applying Effects

Effects are applied to clips using the `with_effects()` method, which takes a list of effect instances.

### Basic Usage

```python
from moviepy.video.fx.Resize import Resize
from moviepy.video.fx.Rotate import Rotate

# Apply a single effect
resized_clip = clip.with_effects([Resize(width=480)])

# Apply multiple effects (order matters)
transformed_clip = clip.with_effects([
    Resize(width=480),
    Rotate(45)
])
```

### Chaining Effects

Effects can be chained by calling `with_effects()` multiple times:

```python
# Chain effects with separate calls
clip = clip.with_effects([Resize(width=480)])
clip = clip.with_effects([Rotate(45)])
```

This is equivalent to:

```python
clip = clip.with_effects([Resize(width=480), Rotate(45)])
```

### Effect Parameters

Each effect has specific parameters that control its behavior. Parameters can be:

- Constant values (numbers, tuples, etc.)
- Functions of time (for dynamic effects)
- None (to use default values)

```python
# Using a function of time for dynamic effects
# This creates a clip that grows in size over time
scale_function = lambda t: 0.5 + t/10.0  # Starts at 0.5x and grows
clip.with_effects([Resize(scale_function)])
```

## Credits Utility

MoviePy also includes a specialized `CreditsClip` utility for creating professional-looking credits sequences.

```python
from moviepy.video.tools.credits import CreditsClip

# Create credits from a text file
credits = CreditsClip(
    "credits.txt",
    width=720,
    font="Impact-Normal",
    font_size=30,
    color="white",
    stroke_color="black",
    stroke_width=1
)
```

The credits file format is specialized for creating credit sequences:

```
# This is a comment
# Leave 4 blank lines
.blank 4

..Executive Story Editor
MARCEL DURAND

..Associate Producers
MARTIN MARCEL
DIDIER MARTIN
```

Sources: [moviepy/video/tools/credits.py:11-143]()

## Creating Custom Effects

Custom effects can be created by extending the `Effect` class and implementing the `apply` method:

```python
from dataclasses import dataclass
from moviepy.Effect import Effect

@dataclass
class MyCustomEffect(Effect):
    """My custom effect description."""
    
    # Define parameters as dataclass fields
    strength: float = 1.0
    
    def apply(self, clip):
        # Implement transformation logic
        def transform_frame(frame):
            # Transform each frame (example)
            return frame * self.strength
            
        # Return transformed clip
        return clip.image_transform(transform_frame)
```

Custom effects can then be used like built-in effects:

```python
clip = clip.with_effects([MyCustomEffect(strength=1.5)])
```

Sources: [moviepy/video/fx/Resize.py:11-158](), [moviepy/video/fx/Rotate.py:11-128]()