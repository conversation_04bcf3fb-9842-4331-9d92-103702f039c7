# Getting Started

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [docs/_static/code/getting_started/moviepy_10_minutes/trailer.py](docs/_static/code/getting_started/moviepy_10_minutes/trailer.py)
- [docs/getting_started/moviepy_10_minutes.rst](docs/getting_started/moviepy_10_minutes.rst)
- [docs/getting_started/quick_presentation.rst](docs/getting_started/quick_presentation.rst)
- [docs/index.rst](docs/index.rst)
- [docs/user_guide/compositing.rst](docs/user_guide/compositing.rst)
- [docs/user_guide/loading.rst](docs/user_guide/loading.rst)
- [docs/user_guide/rendering.rst](docs/user_guide/rendering.rst)
- [moviepy/audio/tools/cuts.py](moviepy/audio/tools/cuts.py)

</details>



MoviePy is a Python library for video editing automation that provides a user-friendly interface for tasks like cutting, composing, and adding effects to videos. This guide will introduce you to the basic concepts and workflow of MoviePy to help you create your first video edits.

For more detailed installation instructions, see [Installation and Requirements](#1.2).

## Installation

You can install MoviePy using pip:

```bash
pip install moviepy
```

MoviePy requires FFmpeg for video and audio processing. You need to have FFmpeg installed on your system for full functionality.

Sources: [docs/index.rst:14-18](), [docs/getting_started/moviepy_10_minutes.rst:21-29]()

## Core Concepts

The central concept in MoviePy is the "clip." A clip represents a segment of media (video or audio) that you can manipulate and compose with other clips.

### MoviePy Clip Hierarchy

```mermaid
classDiagram
    class Clip {
        +duration
        +start
        +end
        +transform()
        +time_transform()
        +with_effects()
        +subclipped()
    }
    
    Clip <|-- VideoClip
    Clip <|-- AudioClip
    
    VideoClip <|-- ImageClip
    VideoClip <|-- ColorClip
    VideoClip <|-- TextClip
    VideoClip <|-- VideoFileClip
    VideoClip <|-- CompositeVideoClip
    
    AudioClip <|-- AudioFileClip
    AudioClip <|-- AudioArrayClip
    AudioClip <|-- CompositeAudioClip
    
    class VideoClip {
        +frame_function
        +size
        +mask
        +audio
        +get_frame()
        +write_videofile()
        +preview()
    }
    
    class AudioClip {
        +frame_function
        +fps
        +get_frame()
        +to_soundarray()
        +write_audiofile()
    }
```

Sources: [docs/getting_started/quick_presentation.rst:60-73]()

The MoviePy system revolves around the `Clip` base class, which has two main subclasses:
- `VideoClip`: Represents visual media (videos, images, text, etc.)
- `AudioClip`: Represents audio media

Each of these has various subclasses for different types of media sources, such as `VideoFileClip` for loading video files, `ImageClip` for loading images, and `TextClip` for creating text.

## Basic Workflow

The typical MoviePy workflow follows these steps:

### MoviePy Video Editing Workflow

```mermaid
flowchart LR
    Input["Input Sources"] --> Clips["Clip Creation"]
    Clips --> Transform["Transformation"]
    Transform --> Composite["Composition"]
    Composite --> Output["Output"]
    
    subgraph "Input Sources"
        VideoFiles["Video Files"]
        ImageFiles["Image Files"]
        AudioFiles["Audio Files"]
        Text["Text"]
        Color["Color"]
    end
    
    subgraph "Clip Creation"
        VideoFileClip["VideoFileClip"]
        ImageClip["ImageClip"]
        AudioFileClip["AudioFileClip"]
        TextClip["TextClip"]
        ColorClip["ColorClip"]
    end
    
    subgraph "Transformation"
        Resize["resize()"]
        Rotate["rotate()"]
        Crop["crop()"]
        TimeTransform["time_transform()"]
        Effects["with_effects()"]
    end
    
    subgraph "Composition"
        CompositeVideoClip["CompositeVideoClip"]
        CompositeAudioClip["CompositeAudioClip"]
        Concatenate["concatenate_videoclips()"]
        ClipsArray["clips_array()"]
    end
    
    subgraph "Output"
        VideoOutput["write_videofile()"]
        AudioOutput["write_audiofile()"]
        GIFOutput["write_gif()"]
        ImageSeq["write_images_sequence()"]
        Preview["preview()"]
    end
```

Sources: [docs/getting_started/quick_presentation.rst:66-73]()

1. **Load resources as clips**: Load videos, images, audio, or create text
2. **Modify clips**: Cut, resize, add effects, etc.
3. **Compose clips**: Combine multiple clips together
4. **Render the final result**: Save the result as a video file, GIF, etc.

## Basic Example

Here's a simple example that demonstrates adding a title to a video:

```python
from moviepy import *

# Load a video file
video = VideoFileClip("myVideo.mp4")

# Create a text clip with a title
title = TextClip("My Movie", font="Arial", font_size=70, color="white")
title = title.with_position('center').with_duration(10)

# Add the title to the video
final_clip = CompositeVideoClip([video, title])

# Write the result to a file
final_clip.write_videofile("output.mp4")
```

This example shows the basic workflow: loading a clip, creating a text clip, composing them together, and rendering the final result.

Sources: [docs/getting_started/quick_presentation.rst:41-46]()

## Loading Clips

MoviePy supports various types of media that can be loaded as clips:

| Clip Type | Creation | Description |
|-----------|----------|-------------|
| VideoFileClip | `VideoFileClip("video.mp4")` | Load a video file |
| ImageClip | `ImageClip("image.jpg")` | Load a static image |
| AudioFileClip | `AudioFileClip("audio.mp3")` | Load an audio file |
| TextClip | `TextClip("Hello", font_size=70, color="white")` | Create text |
| ColorClip | `ColorClip((width, height), color=(255, 0, 0))` | Create solid color |

Sources: [docs/user_guide/loading.rst:11-19](), [docs/getting_started/moviepy_10_minutes/trailer.py:1-10]()

## Modifying Clips

Once clips are loaded, they can be modified in various ways:

### Cutting and Trimming

```python
# Extract a segment from a clip
clip = video.subclipped(10, 20)  # Get segment from 10s to 20s

# Remove a section from a clip
clip = clip.with_section_cut_out(start_time=4, end_time=10)
```

Sources: [docs/getting_started/moviepy_10_minutes/trailer.py:13-25](), [docs/getting_started/moviepy_10_minutes/trailer.py:41-54]()

### Timing and Positioning

```python
# Set when a clip starts and its duration
clip = clip.with_start(5).with_duration(10)  # Starts at 5s, lasts 10s

# Position a clip in the composition
clip = clip.with_position(("center", "center"))  # Center of the screen
```

Sources: [docs/getting_started/moviepy_10_minutes/trailer.py:84-111](), [docs/getting_started/moviepy_10_minutes/trailer.py:138-174]()

### Adding Effects

```python
# Add fade in and fade out effects
clip = clip.with_effects([
    vfx.FadeIn(1),  # 1 second fade in
    vfx.FadeOut(1)  # 1 second fade out
])

# Add audio effects
clip = clip.with_effects([
    afx.AudioFadeIn(1),
    afx.AudioFadeOut(1)
])
```

Sources: [docs/getting_started/moviepy_10_minutes/trailer.py:176-239]()

## Compositing Clips

Compositing is the process of combining multiple clips:

### Overlaying Clips

```python
# Overlay clips (later clips appear on top)
composite = CompositeVideoClip([background_clip, overlay_clip])
```

### Concatenating Clips

```python
# Play clips one after another
final = concatenate_videoclips([clip1, clip2, clip3])
```

### Arranging Clips in a Grid

```python
# Arrange clips in a grid
grid = clips_array([[clip1, clip2], 
                   [clip3, clip4]])
```

Sources: [docs/user_guide/compositing.rst:19-106]()

## Previewing and Rendering

### Previewing Clips

```python
# Preview a clip (requires ffplay)
clip.preview(fps=20)

# Show a specific frame
clip.show(t=5)  # Show frame at 5 seconds
```

Sources: [docs/getting_started/moviepy_10_minutes/trailer.py:28-38](), [docs/user_guide/rendering.rst:8-54]()

### Rendering to Files

```python
# Write to video file
clip.write_videofile("output.mp4")

# Create a GIF
clip.write_gif("animation.gif")

# Export audio
clip.write_audiofile("audio.mp3")

# Export frames as images
clip.write_images_sequence("frame%d.jpg")
```

Sources: [docs/user_guide/rendering.rst:81-145](), [docs/getting_started/moviepy_10_minutes/trailer.py:286-307]()

## Advanced Example: Creating a Trailer

Let's build a more complex example by creating a movie trailer with multiple clips, effects, and transitions:

```python
# Load clips for different scenes
intro_clip = video.subclipped(0, 10)
scene1 = video.subclipped(20, 30)
scene2 = video.subclipped(45, 55)

# Add text overlays
title = TextClip("My Amazing Movie", font_size=70, color="white")
title = title.with_position("center").with_duration(5).with_start(2)

credits = TextClip("Directed by Me", font_size=50, color="white")
credits = credits.with_position("center").with_duration(3).with_start(15)

# Add effects to clips
intro_clip = intro_clip.with_effects([vfx.FadeIn(1), vfx.FadeOut(1)])
scene1 = scene1.with_effects([vfx.FadeIn(1), vfx.FadeOut(1)])
scene2 = scene2.with_effects([vfx.FadeIn(1), vfx.FadeOut(1)])

# Combine everything
final_clip = CompositeVideoClip([
    concatenate_videoclips([intro_clip, scene1, scene2]),
    title,
    credits
])

# Render the trailer
final_clip.write_videofile("trailer.mp4")
```

Sources: [docs/getting_started/moviepy_10_minutes/trailer.py:13-307]()

## Next Steps

This guide has covered the basics of MoviePy. To learn more:

- For details on video/audio effects, see [Video Effects](#3.1) and [Audio Effects](#3.2)
- For advanced compositing techniques, see [Compositing](#3.3)
- For I/O operations, see [Reading Media](#4.2) and [Writing Media](#4.3)

Now you're ready to start creating your own video edits with MoviePy!