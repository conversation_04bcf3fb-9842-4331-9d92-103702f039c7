# I/O System

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/audio/io/ffmpeg_audiowriter.py](moviepy/audio/io/ffmpeg_audiowriter.py)
- [moviepy/audio/io/ffplay_audiopreviewer.py](moviepy/audio/io/ffplay_audiopreviewer.py)
- [moviepy/audio/io/readers.py](moviepy/audio/io/readers.py)
- [moviepy/config.py](moviepy/config.py)
- [moviepy/tools.py](moviepy/tools.py)
- [moviepy/video/io/ffmpeg_tools.py](moviepy/video/io/ffmpeg_tools.py)
- [moviepy/video/io/ffmpeg_writer.py](moviepy/video/io/ffmpeg_writer.py)
- [moviepy/video/io/gif_writers.py](moviepy/video/io/gif_writers.py)
- [moviepy/video/tools/subtitles.py](moviepy/video/tools/subtitles.py)
- [tests/test_AudioClips.py](tests/test_AudioClips.py)
- [tests/test_PR.py](tests/test_PR.py)
- [tests/test_ffmpeg_reader.py](tests/test_ffmpeg_reader.py)
- [tests/test_ffmpeg_tools.py](tests/test_ffmpeg_tools.py)

</details>



The I/O (Input/Output) system in MoviePy is responsible for reading from and writing to various media formats. It provides the foundational functionality that allows MoviePy to work with video files, audio files, images, and other media formats. This page explains the architecture of MoviePy's I/O system, the components that make it up, and how it integrates with external tools like FFmpeg.

For information about specific effects and transformations applied to media, see [Video Effects](#3.1) and [Audio Effects](#3.2). For information about combining multiple clips, see [Compositing](#3.3).

## Overview

MoviePy's I/O system is primarily built around FFmpeg integration. It provides abstractions for:

1. **Reading media files** - Converting external media formats into internal representations
2. **Writing media files** - Converting MoviePy's internal representations into standard media formats
3. **Preview functionality** - Allowing real-time playback during development
4. **Utility operations** - Performing common I/O operations like extraction, resizing, and merging

```mermaid
flowchart TD
    subgraph "MoviePy I/O System"
        direction TB
        Input["Input Media"] --> Reader["Media Readers"]
        Reader --> InternalRep["Internal Representation"]
        InternalRep --> Writer["Media Writers"]
        Writer --> Output["Output Media"]
        InternalRep --> Previewer["Media Previewers"]
        
        Reader --> FFmpegConfig["FFmpeg Configuration"]
        Writer --> FFmpegConfig
        Previewer --> FFmpegConfig
        
        FFmpegTools["FFmpeg Tools"] --> FFmpegConfig
    end
    
    subgraph "External Dependencies"
        FFmpeg["FFmpeg Binary"]
        FFplay["FFplay Binary"]
        ImageIO["ImageIO Library"]
        Pillow["Pillow Library"]
    end
    
    FFmpegConfig --> FFmpeg
    FFmpegConfig --> FFplay
    Writer --> ImageIO
    Reader --> Pillow
```

Sources: [moviepy/config.py:1-92](), [moviepy/tools.py:1-315]()

## FFmpeg Integration

At the heart of MoviePy's I/O system is its integration with FFmpeg, a powerful multimedia framework capable of handling most audio and video formats. MoviePy uses FFmpeg for both reading and writing media files.

### Configuration

The FFmpeg integration begins with locating the FFmpeg binary on the system, handled by the `config.py` module. MoviePy can use FFmpeg in several ways:

1. Use the FFmpeg binary from imageio (`ffmpeg-imageio`)
2. Auto-detect FFmpeg on the system path (`auto-detect`)
3. Use a user-specified path

```mermaid
flowchart LR
    subgraph "FFmpeg Configuration"
        direction TB
        Start["Start"] --> EnvCheck["Check Environment Variables"]
        
        EnvCheck --> IsImageio{"Is FFMPEG_BINARY = 'ffmpeg-imageio'?"}
        IsImageio -->|Yes| UseImageioFFmpeg["Use FFmpeg from imageio"]
        
        IsImageio -->|No| IsAutoDetect{"Is FFMPEG_BINARY = 'auto-detect'?"}
        IsAutoDetect -->|Yes| TryDefault["Try 'ffmpeg' or 'ffmpeg.exe'"]
        
        IsAutoDetect -->|No| TryUserPath["Try user-specified path"]
        
        UseImageioFFmpeg --> FFmpegReady["FFmpeg Ready"]
        TryDefault --> FFmpegReady
        TryUserPath --> FFmpegReady
    end
```

Sources: [moviepy/config.py:1-92]()

### FFmpeg Utilities

MoviePy provides several utility functions for working with FFmpeg directly:

- `ffmpeg_escape_filename`: Escapes filenames for FFmpeg
- `cross_platform_popen_params`: Ensures compatibility across different platforms
- `subprocess_call`: Executes FFmpeg commands with proper logging

These utilities are used throughout the I/O system to ensure consistent interaction with FFmpeg.

Sources: [moviepy/tools.py:14-51](), [moviepy/tools.py:53-61]()

## Reading Media

MoviePy uses dedicated reader classes to read and process video and audio files.

### Video Reading

The `FFMPEG_VideoReader` class handles reading video files and provides frame-by-frame access to video content. Key features include:

- Buffered reading to improve performance
- Frame-accurate seeking
- Support for various pixel formats
- Metadata extraction

```mermaid
flowchart TD
    subgraph "Video Reading Process"
        VideoFile["Video File"] --> |"ffmpeg_parse_infos"| MetadataExtraction["Extract Metadata"]
        MetadataExtraction --> FFmpegProcess["Initialize FFmpeg Process"]
        FFmpegProcess --> ReadFirstFrame["Read First Frame"]
        ReadFirstFrame --> VideoReaderReady["FFMPEG_VideoReader Ready"]
        
        VideoReaderReady --> |"get_frame(t)"| SeekOperation["Seek to Position"]
        SeekOperation --> BufferCheck{"Frame in Buffer?"}
        BufferCheck -->|Yes| ReturnFrame["Return Frame from Buffer"]
        BufferCheck -->|No| ReadNewFrames["Read New Frames into Buffer"]
        ReadNewFrames --> ReturnFrame
    end
```

Sources: [tests/test_ffmpeg_reader.py:15-19](), [tests/test_ffmpeg_reader.py:600-647]()

### Audio Reading

The `FFMPEG_AudioReader` class provides similar functionality for audio files:

- Reading audio data in chunks
- Converting audio to numpy arrays
- Support for different sample rates and bit depths
- Buffered reading for performance

```mermaid
flowchart TD
    subgraph "Audio Reading Process"
        AudioFile["Audio File"] --> |"ffmpeg_parse_infos"| MetadataExtraction["Extract Metadata"]
        MetadataExtraction --> FFmpegProcess["Initialize FFmpeg Process"]
        FFmpegProcess --> ReadFirstChunk["Read First Chunk"]
        ReadFirstChunk --> AudioReaderReady["FFMPEG_AudioReader Ready"]
        
        AudioReaderReady --> |"get_frame(t)"| TimeToFrameNumber["Convert Time to Frame Number"]
        TimeToFrameNumber --> BufferCheck{"Frame in Buffer?"}
        BufferCheck -->|Yes| ReturnAudio["Return Audio Data from Buffer"]
        BufferCheck -->|No| BufferOperation["Buffer Around Frame"]
        BufferOperation --> ReturnAudio
    end
```

Sources: [moviepy/audio/io/readers.py:13-304]()

## Writing Media

MoviePy provides classes and functions for writing video, audio, and image data to files.

### Video Writing

The `FFMPEG_VideoWriter` class is responsible for writing video data to files:

- Supports various codecs and formats
- Handles pixel format conversion
- Manages audio and video muxing
- Provides detailed error messages for troubleshooting

```mermaid
flowchart TD
    subgraph "Video Writing Process"
        Clip["VideoClip"] --> |"write_videofile()"| FFmpegVideoWriter["Initialize FFMPEG_VideoWriter"]
        FFmpegVideoWriter --> SetupFFmpeg["Setup FFmpeg Process"]
        SetupFFmpeg --> IterFrames["Iterate Through Frames"]
        IterFrames --> |"For each frame"| WriteFrame["Write Frame to FFmpeg"]
        WriteFrame --> CheckMore{"More Frames?"}
        CheckMore -->|Yes| IterFrames
        CheckMore -->|No| CloseWriter["Close Writer"]
        CloseWriter --> FinalVideo["Final Video File"]
    end
```

Sources: [moviepy/video/io/ffmpeg_writer.py:15-241](), [moviepy/video/io/ffmpeg_writer.py:242-301]()

### Audio Writing

The `FFMPEG_AudioWriter` class handles writing audio data:

- Supports various audio codecs
- Handles different sample rates and bit depths
- Can write standalone audio or mux with video
- Provides error handling with descriptive messages

```mermaid
flowchart TD
    subgraph "Audio Writing Process"
        AudioClip["AudioClip"] --> |"write_audiofile()"| FFmpegAudioWriter["Initialize FFMPEG_AudioWriter"]
        FFmpegAudioWriter --> SetupFFmpeg["Setup FFmpeg Process"]
        SetupFFmpeg --> IterChunks["Iterate Through Audio Chunks"]
        IterChunks --> |"For each chunk"| WriteChunk["Write Chunk to FFmpeg"]
        WriteChunk --> CheckMore{"More Chunks?"}
        CheckMore -->|Yes| IterChunks
        CheckMore -->|No| CloseWriter["Close Writer"]
        CloseWriter --> FinalAudio["Final Audio File"]
    end
```

Sources: [moviepy/audio/io/ffmpeg_audiowriter.py:12-183](), [moviepy/audio/io/ffmpeg_audiowriter.py:186-229]()

### Image Writing

MoviePy can also write individual frames as images:

- The `ffmpeg_write_image` function writes a frame to an image file
- Supports various pixel formats and image types
- Handles both RGB and RGBA data

Sources: [moviepy/video/io/ffmpeg_writer.py:302-363]()

### GIF Writing

For creating animated GIFs, MoviePy provides specialized functions:

- `write_gif_with_imageio`: Uses the ImageIO library to write GIF files

```mermaid
flowchart LR
    subgraph "GIF Writing Options"
        Clip["VideoClip"] --> |"write_gif()"| Options{"Method?"}
        Options -->|"imageio"| ImageioWriter["write_gif_with_imageio()"]
        ImageioWriter --> GIF["GIF File"]
    end
```

Sources: [moviepy/video/io/gif_writers.py:1-21]()

## Media Previewing

MoviePy allows previewing media during development:

### Audio Previewing

The `FFPLAY_AudioPreviewer` class uses FFplay to preview audio:

- Streams audio data directly to FFplay
- Supports synchronization with video preview
- Handles different sample rates and bit depths

```mermaid
flowchart TD
    subgraph "Audio Preview Process"
        AudioClip["AudioClip"] --> |"preview()"| FFplayAudioPreviewer["Initialize FFPLAY_AudioPreviewer"]
        FFplayAudioPreviewer --> SetupFFplay["Setup FFplay Process"]
        SetupFFplay --> IterChunks["Iterate Through Audio Chunks"]
        IterChunks --> |"For each chunk"| WriteChunk["Stream Chunk to FFplay"]
        WriteChunk --> CheckMore{"More Chunks?"}
        CheckMore -->|Yes| IterChunks
        CheckMore -->|No| CloseProcess["Close FFplay"]
    end
```

Sources: [moviepy/audio/io/ffplay_audiopreviewer.py:11-110](), [moviepy/audio/io/ffplay_audiopreviewer.py:112-164]()

## FFmpeg Tools

MoviePy provides higher-level functions for common operations using FFmpeg:

### Extraction and Manipulation

- `ffmpeg_extract_subclip`: Extracts a portion of a video file
- `ffmpeg_merge_video_audio`: Merges separate video and audio files
- `ffmpeg_extract_audio`: Extracts audio from a video file
- `ffmpeg_resize`: Resizes a video file
- `ffmpeg_stabilize_video`: Stabilizes a shaky video

```mermaid
flowchart LR
    subgraph "FFmpeg Tools"
        direction TB
        Input["Input Media"] --> |"ffmpeg_extract_subclip()"| Subclip["Video Subclip"]
        Input --> |"ffmpeg_extract_audio()"| AudioTrack["Audio Track"]
        Input --> |"ffmpeg_resize()"| ResizedVideo["Resized Video"]
        Input --> |"ffmpeg_stabilize_video()"| StableVideo["Stabilized Video"]
        VideoTrack["Video Track"] --> |"ffmpeg_merge_video_audio()"| MergedMedia["Merged Media"]
        AudioTrack --> |"ffmpeg_merge_video_audio()"| MergedMedia
    end
```

Sources: [moviepy/video/io/ffmpeg_tools.py:14-59](), [moviepy/video/io/ffmpeg_tools.py:62-104](), [moviepy/video/io/ffmpeg_tools.py:107-137](), [moviepy/video/io/ffmpeg_tools.py:140-165](), [moviepy/video/io/ffmpeg_tools.py:169-211]()

### Version Information

MoviePy includes functions to check the versions of FFmpeg and FFplay:

- `ffmpeg_version()`: Returns the version of FFmpeg
- `ffplay_version()`: Returns the version of FFplay

These are useful for compatibility checks and debugging.

Sources: [moviepy/video/io/ffmpeg_tools.py:214-252](), [moviepy/video/io/ffmpeg_tools.py:255-290]()

## Additional I/O Features

### Subtitle Support

MoviePy includes support for subtitles through the `SubtitlesClip` class:

- Reads subtitle files (SRT format)
- Creates text clips for each subtitle entry
- Handles timing and positioning
- Supports custom text appearance and styling

```mermaid
flowchart TD
    subgraph "Subtitle Processing"
        SRTFile["SRT File"] --> |"file_to_subtitles()"| SubtitleEntries["Subtitle Entries"]
        SubtitleEntries --> |"SubtitlesClip constructor"| SubtitlesClip["SubtitlesClip"]
        SubtitlesClip --> |"get_frame(t)"| FindSubtitle["Find Subtitle at Time t"]
        FindSubtitle --> |"If subtitle exists"| GenerateText["Generate Text Clip"]
        FindSubtitle --> |"If no subtitle"| EmptyFrame["Return Empty Frame"]
        GenerateText --> ReturnFrame["Return Frame"]
    end
```

Sources: [moviepy/video/tools/subtitles.py:12-198]()

## I/O System Workflow

The complete MoviePy I/O system workflow can be summarized as follows:

```mermaid
flowchart TD
    subgraph "MoviePy I/O Workflow"
        Input["Input Media"] --> |"Reading"| Readers["Media Readers"]
        Readers --> Clips["Clip Objects"]
        Clips --> |"Processing"| ProcessedClips["Processed Clips"]
        ProcessedClips --> |"Writing"| Writers["Media Writers"]
        Writers --> Output["Output Media"]
        
        Clips --> |"Preview"| Previewers["Media Previewers"]
        
        subgraph "Reading Components"
            FFMPEGVideoReader["FFMPEG_VideoReader"]
            FFMPEGAudioReader["FFMPEG_AudioReader"]
            FFmpegInfosParser["FFmpegInfosParser"]
        end
        
        subgraph "Writing Components"
            FFMPEGVideoWriter["FFMPEG_VideoWriter"]
            FFMPEGAudioWriter["FFMPEG_AudioWriter"]
            ImageIOGifWriter["ImageIO GIF Writer"]
        end
        
        subgraph "Preview Components"
            FFPLAYAudioPreviewer["FFPLAY_AudioPreviewer"]
        end
        
        Readers --- Reading["Reading Components"]
        Writers --- Writing["Writing Components"]
        Previewers --- Preview["Preview Components"]
    end
```

Sources: [moviepy/video/io/ffmpeg_writer.py:15-301](), [moviepy/audio/io/readers.py:13-304](), [moviepy/audio/io/ffmpeg_audiowriter.py:12-229](), [moviepy/video/io/gif_writers.py:1-21](), [moviepy/audio/io/ffplay_audiopreviewer.py:11-164]()

## Integration with Clip System

MoviePy's I/O system integrates closely with the Clip system, providing the bridge between external media formats and the internal clip representation:

```mermaid
flowchart TD
    subgraph "I/O and Clip System Integration"
        Videos["Video Files"] --> |"VideoFileClip"| VideoReader["FFMPEG_VideoReader"]
        VideoReader --> VideoClip["VideoClip"]
        
        Audio["Audio Files"] --> |"AudioFileClip"| AudioReader["FFMPEG_AudioReader"]
        AudioReader --> AudioClip["AudioClip"]
        
        VideoClip --> |"write_videofile()"| VideoWriter["FFMPEG_VideoWriter"]
        VideoWriter --> OutputVideo["Output Video"]
        
        AudioClip --> |"write_audiofile()"| AudioWriter["FFMPEG_AudioWriter"]
        AudioWriter --> OutputAudio["Output Audio"]
        
        VideoClip --> |"write_gif()"| GifWriter["GIF Writer"]
        GifWriter --> OutputGif["Output GIF"]
    end
```

Sources: [moviepy/video/io/ffmpeg_writer.py:242-301](), [moviepy/audio/io/ffmpeg_audiowriter.py:186-229](), [moviepy/video/io/gif_writers.py:9-21]()

## Error Handling

The I/O system includes robust error handling mechanisms to provide clear feedback when issues occur:

- Detailed error messages for common FFmpeg problems
- Specific error handling for codec problems
- Guidance for resolving bitrate, format, and compatibility issues
- Resource cleanup to prevent memory leaks

For more detailed information about FFmpeg integration specifically, see [FFmpeg Integration](#4.1).

Sources: [moviepy/video/io/ffmpeg_writer.py:166-221](), [moviepy/audio/io/ffmpeg_audiowriter.py:112-159]()