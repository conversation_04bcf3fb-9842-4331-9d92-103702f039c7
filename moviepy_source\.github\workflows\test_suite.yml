# This workflow installs Python dependencies, runs tests and lints
# using a variety of Python versions.
# For more information see:
# https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Run Test Suite

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  # Uses Python Framework build because on macOS, Matplotlib requires it
  macos:
    runs-on: macos-13
    # Do not ignore bash profile files. From:
    # https://github.com/marketplace/actions/setup-miniconda
    defaults:
      run:
        shell: bash -l {0}
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]
    steps:
    - uses: actions/checkout@v4.1.6
    - uses: conda-incubator/setup-miniconda@v3
      with:
        auto-update-conda: true
        python-version: ${{ matrix.python-version }}
        auto-activate-base: true

    - name: Install pythonw
      run: conda install python.app

    - name: Python Version Info
      run: |
        pythonw --version
        which python
        pythonw -m site --user-site
        echo $PYTHONPATH
        echo $PYTHONHOME

    - name: Install dependencies
      run: |
        # needed for installing mat<PERSON><PERSON><PERSON>b
        brew install pkg-config
        python -m pip install --upgrade wheel setuptools coveralls
        python -m pip install ".[test, optional]"

    - name: Test with pytest
      run: |
        pythonw -m pytest --doctest-glob "moviepy/**/**.py" -v --cov moviepy --cov-report term-missing


  windows:
    runs-on: windows-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]
      fail-fast: false
    steps:
      - uses: actions/checkout@v4.1.6
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5.1.0
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade wheel setuptools coveralls
          python -m pip install ".[test]"

      - name: Check third party dependencies
        run: |
          python moviepy\config.py

      - name: PyTest
        shell: cmd
        run: |
          python -m pytest --cov moviepy --cov-report term-missing

  linux:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]
      fail-fast: false
    steps:
      - uses: actions/checkout@v4.1.6

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5.1.0
        with:
          python-version: ${{ matrix.python-version }}

      - name: Python Version Info
        run: |
          python --version
          which python
          python -m site --user-site

      - name: Install common requirements
        run: |
          python -m pip install --upgrade wheel setuptools

      - name: Install test requirements
        if: ${{ matrix.python-version == '3.7' }}
        run: |
          python -m pip install ".[test, doc]"
          python -m pip install python-dotenv

      - name: Install test and optional requirements
        if: ${{ matrix.python-version != '3.7' }}
        run: |
          python -m pip install ".[test, optional, doc]"

      - name: PyTest
        run: |
          python -m pytest --doctest-glob "moviepy/**/**.py" --cov moviepy --cov-report term-missing

      - name: Test pip installation
        run: |
          pip install -e .
          pip install -e .[optional]
          pip install -e .[test]
          pip install -e .[doc]
