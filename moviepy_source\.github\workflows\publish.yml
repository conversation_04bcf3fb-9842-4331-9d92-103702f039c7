name: Publish to PyPI

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: release
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: pip
      - name: Install dependencies
        run: |
          pip install setuptools wheel build
      - name: Build
        run: |
          python -m build
      - name: Publish
        uses: pypa/gh-action-pypi-publish@release/v1
