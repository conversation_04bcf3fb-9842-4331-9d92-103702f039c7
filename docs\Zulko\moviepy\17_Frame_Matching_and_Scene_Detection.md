# Frame Matching and Scene Detection

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/video/io/ImageSequenceClip.py](moviepy/video/io/ImageSequenceClip.py)
- [moviepy/video/tools/cuts.py](moviepy/video/tools/cuts.py)
- [tests/test_videotools.py](tests/test_videotools.py)

</details>



This document describes the frame matching and scene detection capabilities in MoviePy, which allow users to identify similar frames across videos, find repeating patterns, and detect scene transitions. These tools are particularly useful for creating perfectly looping GIFs, identifying video periods, and segmenting videos into distinct scenes.

## 1. Overview

MoviePy provides two primary systems for analyzing frame content and transitions:

1. **Frame Matching**: Identifies pairs of frames that look similar within a video clip
2. **Scene Detection**: Identifies points where scenes change based on significant luminosity differences

These systems serve as building blocks for advanced video analysis and editing operations, enabling tasks like automatic GIF loop creation and video segmentation.

```mermaid
flowchart TD
    subgraph "Frame Analysis Systems"
        direction LR
        FM["Frame Matching"]
        SD["Scene Detection"]
    end
    
    subgraph "Frame Matching Components"
        direction TB
        FramesMatch["FramesMatch Class"]
        FramesMatches["FramesMatches Class"]
        FindPeriod["find_video_period()"]
    end
    
    subgraph "Scene Detection Components"
        direction TB
        DetectScenes["detect_scenes()"]
        LuminosityAnalysis["Luminosity Analysis"]
    end
    
    FM --> FramesMatch
    FM --> FramesMatches
    FM --> FindPeriod
    SD --> DetectScenes
    DetectScenes --> LuminosityAnalysis
    
    subgraph "Applications"
        direction LR
        LoopingGifs["Looping GIFs"]
        PeriodDetection["Video Period Detection"]
        SceneSegmentation["Scene Segmentation"]
    end
    
    FramesMatches --> LoopingGifs
    FindPeriod --> PeriodDetection
    DetectScenes --> SceneSegmentation
```

Sources: [moviepy/video/tools/cuts.py:1-523]()

## 2. Frame Matching System

The frame matching system allows you to find pairs of frames in a video that look similar to each other. This is particularly useful for creating seamlessly looping GIFs by identifying good start and end points.

### 2.1 Core Components

```mermaid
classDiagram
    class FramesMatch {
        +start_time: float
        +end_time: float
        +min_distance: float
        +max_distance: float
        +time_span: float
        +__iter__()
        +__eq__(other)
    }
    
    class FramesMatches {
        +__init__(lst)
        +best(n, percent)
        +filter(condition)
        +save(filename)
        +load(filename)
        +from_clip(clip, distance_threshold, max_duration, fps, logger)
        +select_scenes(match_threshold, min_time_span, nomatch_threshold, time_distance)
        +write_gifs(clip, gifs_dir, **kwargs)
    }
    
    class Functions {
        +find_video_period(clip, fps, start_time)
    }
    
    FramesMatches --o FramesMatch : "contains"
```

Sources: [moviepy/video/tools/cuts.py:50-98](), [moviepy/video/tools/cuts.py:101-460]()

### 2.2 FramesMatch Class

The `FramesMatch` class represents a pair of matching frames in a video:

- **start_time**: The timestamp of the first frame
- **end_time**: The timestamp of the second frame
- **min_distance**: Lower bound on the distance between frames
- **max_distance**: Upper bound on the distance between frames
- **time_span**: Duration between start and end frames (computed as `end_time - start_time`)

A smaller distance indicates greater similarity between frames.

Sources: [moviepy/video/tools/cuts.py:50-98]()

### 2.3 FramesMatches Class

The `FramesMatches` class is a container for multiple `FramesMatch` objects with methods for analyzing and using the matches:

- **best(n=1, percent=None)**: Returns the best matches (either a specific number or percentage)
- **filter(condition)**: Filters matches based on a custom condition
- **save(filename)/load(filename)**: Serializes/deserializes matches to/from a file
- **from_clip()**: Static method that analyzes a clip to find matching frames
- **select_scenes()**: Filters matches to identify the best loop points
- **write_gifs()**: Creates GIF files from the matching frame pairs

The matching algorithm works by comparing the visual similarity between frames:

```mermaid
flowchart TD
    A["Input Video Clip"] --> B["Extract Frames"]
    B --> C["Compare Frame Pairs"]
    C --> D["Calculate Frame Distances"]
    D --> E["Filter by Distance Threshold"]
    E --> F["FramesMatches Result"]
    
    subgraph "Frame Distance Calculation"
        G["Compute Dot Product"] --> H["Normalize Frames"]
        H --> I["Calculate Euclidean Distance"]
    end
    
    C --> G
```

Sources: [moviepy/video/tools/cuts.py:101-460]()

### 2.4 Finding Video Periods

The `find_video_period` function detects repeating patterns in a video by analyzing frame correlations. This is useful for identifying rhythmic or looping content:

```python
from moviepy import *
from moviepy.video.tools.cuts import find_video_period

clip = VideoFileClip("my_video.mp4").subclipped(0, 5)
period = find_video_period(clip, fps=30)
print(f"Video repeats approximately every {period} seconds")
```

The function works by:
1. Extracting frames at regular intervals
2. Calculating correlation between the first frame and subsequent frames
3. Identifying the time point with maximum correlation, which indicates a likely repetition

Sources: [moviepy/video/tools/cuts.py:10-47]()

## 3. Scene Detection

The scene detection system identifies points in a video where one scene transitions to another, which is useful for automatically segmenting videos.

### 3.1 How Scene Detection Works

MoviePy's scene detection uses frame luminosity (brightness) to identify scene changes. The algorithm:

1. Computes the luminosity of each frame
2. Calculates the absolute difference in luminosity between consecutive frames
3. Identifies "jumps" where the difference exceeds a threshold
4. Returns a list of scene cuts with start and end times

```mermaid
flowchart LR
    A["Input Clip"] --> B["Extract Frame Luminosities"]
    B --> C["Calculate Luminosity Differences"]
    C --> D["Compute Average Difference"]
    D --> E["Identify Significant Jumps"]
    E --> F["Generate Scene Cuts"]
    
    subgraph "Detect Scenes Function"
        direction TB
        Input["clip, luminosity_threshold, fps"]
        Process["detect_scenes()"]
        Output["cuts, luminosities"]
        Input --> Process
        Process --> Output
    end
```

Sources: [moviepy/video/tools/cuts.py:462-522]()

### 3.2 Using Scene Detection

The `detect_scenes` function takes a video clip and returns a list of scenes:

```python
from moviepy import *
from moviepy.video.tools.cuts import detect_scenes

clip = VideoFileClip("my_movie.mp4")
scenes, luminosities = detect_scenes(clip, luminosity_threshold=10)

# scenes is a list of (start_time, end_time) tuples
for i, (start, end) in enumerate(scenes):
    print(f"Scene {i+1}: {start:.2f}s - {end:.2f}s (duration: {end-start:.2f}s)")
```

The `luminosity_threshold` parameter controls sensitivity - lower values detect more subtle scene changes, while higher values only detect major transitions.

Sources: [moviepy/video/tools/cuts.py:462-522]()

## 4. Practical Applications

### 4.1 Creating Perfect Loop GIFs

One of the most powerful applications is automatically creating seamlessly looping GIFs:

```python
from moviepy import *
from moviepy.video.tools.cuts import FramesMatches

# Load a video clip
clip = VideoFileClip("interesting_sequence.mp4")

# Find frames that match within the clip
matches = FramesMatches.from_clip(
    clip, 
    distance_threshold=10,  # Max distance between matching frames
    max_duration=3,         # Max time span to search
    logger="bar"            # Show progress bar
)

# Filter to get good matches with enough duration
good_matches = matches.filter(lambda m: m.time_span > 1.5)
best_match = good_matches.best()  # Get the best match

# Create a GIF that loops perfectly
loop_clip = clip.subclipped(best_match.start_time, best_match.end_time)
loop_clip.write_gif("perfect_loop.gif")
```

This automatically finds sections of your video that can be looped seamlessly.

Sources: [moviepy/video/tools/cuts.py:101-460](), [tests/test_videotools.py:68-77]()

### 4.2 Analyzing Video Structure

You can use scene detection to analyze the structure of a video:

```python
from moviepy import *
from moviepy.video.tools.cuts import detect_scenes
import numpy as np
import matplotlib.pyplot as plt

clip = VideoFileClip("movie.mp4")
scenes, luminosities = detect_scenes(clip, luminosity_threshold=15)

# Plot the luminosity over time to visualize scene structure
plt.figure(figsize=(15, 5))
plt.plot(np.linspace(0, clip.duration, len(luminosities)), luminosities)
plt.xlabel("Time (seconds)")
plt.ylabel("Luminosity")
plt.title("Video Scene Structure")

# Add vertical lines for scene changes
for start, _ in scenes[1:]:  # Skip the first scene (starts at 0)
    plt.axvline(x=start, color='r', linestyle='--', alpha=0.5)

plt.savefig("scene_structure.png")
```

This generates a visualization of your video's scene structure and brightness patterns.

Sources: [moviepy/video/tools/cuts.py:462-522](), [tests/test_videotools.py:68-77]()

### 4.3 Batch Processing of Matching Frames

For more complex applications, you can process all matching frames:

```python
import os
from moviepy import *
from moviepy.video.tools.cuts import FramesMatches

# Create a directory to store GIFs
if not os.path.exists("loops"):
    os.mkdir("loops")

clip = VideoFileClip("source_video.mp4")
matches = FramesMatches.from_clip(clip, 10, 2)
scenes = matches.select_scenes(
    match_threshold=1,        # Maximum distance for a good match
    min_time_span=1.0,        # Minimum scene duration 
    nomatch_threshold=8       # Minimum distance for a bad match
)

# Generate GIFs for all good loops
scenes.write_gifs(clip, "loops")
```

This finds all good looping sections and saves each as a separate GIF file.

Sources: [moviepy/video/tools/cuts.py:315-460](), [tests/test_videotools.py:320-362]()

## 5. Technical Implementation Details

### 5.1 Frame Distance Calculation

The frame matching algorithm uses a dot product-based distance metric to compare frames efficiently. The implementation in `FramesMatches.from_clip()` computes:

1. The norm of each frame (dot product with itself)
2. The dot product between pairs of frames
3. The Euclidean distance derived from these values

To optimize performance, the algorithm:
- Discards frame pairs that can't match based on norm differences
- Uses triangle inequality to quickly filter out non-matching frames
- Only computes full distances for potentially matching frame pairs

This allows processing large videos in reasonable time.

Sources: [moviepy/video/tools/cuts.py:200-313]()

### 5.2 Integration with Other MoviePy Components

Frame matching and scene detection integrate with MoviePy's clip system:

```mermaid
flowchart TD
    subgraph "MoviePy Core"
        VideoClip["VideoClip"]
        AudioClip["AudioClip"]
        Effects["Effects System"]
    end
    
    subgraph "Frame Analysis Tools"
        FrameMatching["Frame Matching System"]
        SceneDetection["Scene Detection System"]
    end
    
    subgraph "Workflow Examples"
        Loop["Create Looping GIFs"]
        Segment["Segment into Scenes"]
        Analyze["Analyze Video Structure"]
    end
    
    VideoClip --> FrameMatching
    VideoClip --> SceneDetection
    
    FrameMatching --> Loop
    SceneDetection --> Segment
    FrameMatching --> Analyze
    SceneDetection --> Analyze
```

This architecture allows seamless integration of analysis results with MoviePy's editing capabilities.

Sources: [moviepy/video/tools/cuts.py:1-523]()

## 6. Conclusion

MoviePy's frame matching and scene detection tools provide powerful capabilities for analyzing video content and structure. These tools enable automated creation of looping GIFs, video segmentation, and rhythm analysis.

For more information about related topics:
- For video effects that can be applied to clips, see [Video Effects](#3.1)
- For general video compositing, see [Compositing](#3.3)
- For image sequence handling that can work with the output of scene detection, see [ImageSequenceClip](#2.2)