# Testing Framework

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [media/subtitles-unicode.srt](media/subtitles-unicode.srt)
- [media/subtitles.srt](media/subtitles.srt)
- [tests/conftest.py](tests/conftest.py)
- [tests/test_ImageSequenceClip.py](tests/test_ImageSequenceClip.py)
- [tests/test_VideoFileClip.py](tests/test_VideoFileClip.py)
- [tests/test_compositing.py](tests/test_compositing.py)
- [tests/test_doc_examples.py](tests/test_doc_examples.py)
- [tests/test_ffmpeg_writer.py](tests/test_ffmpeg_writer.py)
- [tests/test_issues.py](tests/test_issues.py)
- [tests/test_tools.py](tests/test_tools.py)

</details>



This page describes the testing framework used in MoviePy. It covers how tests are organized, the tools and fixtures used for testing, and guidelines for writing effective tests for the library. For information about the CI/CD workflow which runs these tests, see [CI/CD Workflow](#6.3).

## Overview

MoviePy uses pytest as its primary testing framework. Tests are organized in several Python files, each focusing on specific components or features of the library. The tests help ensure the library's reliability by verifying that various components work correctly and catching regressions.

```mermaid
flowchart TD
    subgraph "Testing Framework"
        Pytest["pytest"]
        TestFiles["Test Files"]
        Fixtures["Test Fixtures"]
        TestUtilities["Test Utilities"]
    end
    
    subgraph "Test Files"
        IssueTests["test_issues.py"]
        ToolsTests["test_tools.py"]
        CompositeTests["test_compositing.py"]
        VideoFileClipTests["test_VideoFileClip.py"]
        FFmpegTests["test_ffmpeg_writer.py"]
        DocTests["test_doc_examples.py"]
    end
    
    subgraph "Test Fixtures"
        Util["util"]
        Video["video"]
        StereoWave["stereo_wave"]
        MonoWave["mono_wave"]
    end
    
    TestFiles --> Pytest
    Fixtures --> Pytest
    TestUtilities --> Pytest
    
    Pytest --> MoviePy["MoviePy Library"]
```

Sources: [tests/conftest.py](), [tests/test_issues.py](), [tests/test_tools.py](), [tests/test_compositing.py](), [tests/test_VideoFileClip.py](), [tests/test_ffmpeg_writer.py](), [tests/test_doc_examples.py]()

## Test Organization

The tests are organized into several Python files, each focusing on testing a specific component or feature of MoviePy:

| Test File | Purpose |
|-----------|---------|
| `test_issues.py` | Tests for specific GitHub issues that have been fixed |
| `test_tools.py` | Tests for utility tools in MoviePy |
| `test_compositing.py` | Tests for video and audio compositing functionality |
| `test_VideoFileClip.py` | Tests for the `VideoFileClip` class |
| `test_ffmpeg_writer.py` | Tests for FFmpeg writing functionality |
| `test_ImageSequenceClip.py` | Tests for `ImageSequenceClip` class |
| `test_doc_examples.py` | Tests for documentation examples |

Each test file contains multiple test functions that verify specific aspects of MoviePy's functionality.

```mermaid
flowchart LR
    subgraph "MoviePy Tests"
        direction TB
        Core["Core Classes Tests"]
        IO["I/O Tests"]
        Effects["Effects Tests"]
        Tools["Tools Tests"]
        Issues["Issue Tests"]
        Doc["Documentation Tests"]
    end
    
    Core --> VideoClipTests["VideoClip Tests"]
    Core --> AudioClipTests["AudioClip Tests"]
    Core --> CompositeTests["Composite Clip Tests"]
    
    IO --> FFmpegReaderTests["FFmpeg Reader Tests"]
    IO --> FFmpegWriterTests["FFmpeg Writer Tests"]
    IO --> ImageSequenceTests["Image Sequence Tests"]
    
    Effects --> VideoEffectsTests["Video Effects Tests"]
    Effects --> AudioEffectsTests["Audio Effects Tests"]
    
    VideoClipTests --> test_VideoFileClip["test_VideoFileClip.py"]
    CompositeTests --> test_compositing["test_compositing.py"]
    FFmpegWriterTests --> test_ffmpeg_writer["test_ffmpeg_writer.py"]
    ImageSequenceTests --> test_ImageSequenceClip["test_ImageSequenceClip.py"]
    Tools --> test_tools["test_tools.py"]
    Issues --> test_issues["test_issues.py"]
    Doc --> test_doc_examples["test_doc_examples.py"]
```

Sources: [tests/test_issues.py](), [tests/test_tools.py](), [tests/test_compositing.py](), [tests/test_VideoFileClip.py](), [tests/test_ffmpeg_writer.py](), [tests/test_ImageSequenceClip.py](), [tests/test_doc_examples.py]()

## Test Fixtures and Utilities

MoviePy uses pytest fixtures to provide common utilities and resources for tests. These fixtures are defined in `conftest.py`.

### Key Fixtures

1. **util**: Provides common utilities like temporary directories and font paths
2. **video**: Function to get a test video clip
3. **stereo_wave**: Function to get a stereo audio wave
4. **mono_wave**: Function to get a mono audio wave
5. **static_files_server**: A server for testing file loading from URLs
6. **moviepy_modules**: Function to get all MoviePy modules
7. **functions_with_decorator_defined**: Function to extract functions with specific decorators

```mermaid
classDiagram
    class MoviepyTestUtils {
        +FONT: string
        +TMP_DIR: string
        +DOC_EXAMPLES_MEDIAS_DIR: string
    }
    
    class TestFixtures {
        +util(): MoviepyTestUtils
        +video(): function
        +stereo_wave(): function
        +mono_wave(): function
        +static_files_server(): ContextManager
        +moviepy_modules(): function
        +functions_with_decorator_defined(): function
    }
    
    class TestUtilities {
        +get_video(start_time, end_time): VideoFileClip
        +get_stereo_wave(left_freq, right_freq): function
        +get_mono_wave(freq): function
        +get_static_files_server(port): ContextManager
        +get_moviepy_modules(): list
        +get_functions_with_decorator_defined(code, decorator_name): list
    }
    
    TestFixtures --> MoviepyTestUtils: provides
    TestFixtures --> TestUtilities: uses
```

Sources: [tests/conftest.py:142-179]()

### Test Utilities

In addition to fixtures, MoviePy tests use utility classes to simplify common testing tasks. For example, the `ClipPixelTest` class in `test_compositing.py` provides a way to verify the pixel colors of a clip at specific timestamps:

```python
class ClipPixelTest:
    ALLOWABLE_COLOR_VARIATION = 3  # from 0-767: how much mismatch do we allow

    def __init__(self, clip):
        self.clip = clip

    def expect_color_at(self, ts, expected, xy=[0, 0]):
        frame = self.clip.frame_function(ts)
        r, g, b = expected
        actual = frame[xy[1]][xy[0]]
        diff = abs(actual[0] - r) + abs(actual[1] - g) + abs(actual[2] - b)

        mismatch = diff > ClipPixelTest.ALLOWABLE_COLOR_VARIATION
        assert (
            not mismatch
        ), "Expected (%02x,%02x,%02x) but got (%02x,%02x,%02x) at timestamp %s" % (
            *expected,
            *actual,
            ts,
        )
```

Sources: [tests/test_compositing.py:12-31]()

## Writing Tests

### Basic Test Structure

Tests in MoviePy follow the standard pytest pattern. Each test function starts with `test_` and uses assert statements to verify expected behavior. Here's a typical test function:

```python
def test_issue_407():
    red = ColorClip((800, 600), color=(255, 0, 0)).with_duration(5)
    red.fps = 30

    assert red.fps == 30
    assert red.w == 800
    assert red.h == 600
    assert red.size == (800, 600)

    # ColorClip has no fps attribute.
    green = ColorClip((640, 480), color=(0, 255, 0)).with_duration(2)
    blue = ColorClip((640, 480), color=(0, 0, 255)).with_duration(2)

    assert green.w == blue.w == 640
    assert green.h == blue.h == 480
    assert green.size == blue.size == (640, 480)

    with pytest.raises(AttributeError):
        green.fps

    with pytest.raises(AttributeError):
        blue.fps

    video = concatenate_videoclips([red, green, blue])
    assert video.fps == red.fps
```

Sources: [tests/test_issues.py:259-284]()

### Parameterized Tests

MoviePy makes extensive use of pytest's parameterization feature to run the same test with different inputs. This approach reduces code duplication and provides better test coverage. Here's an example:

```python
@pytest.mark.parametrize(
    ("given", "expected"),
    [
        ("libx264", "mp4"),
        ("libmpeg4", "mp4"),
        ("libtheora", "ogv"),
        ("libvpx", "webm"),
        ("jpeg", "jpeg"),
    ],
)
def test_find_extensions(given, expected):
    """Test for find_extension function."""
    assert tools.find_extension(given) == expected
```

Sources: [tests/test_tools.py:15-27]()

### Testing Issues

MoviePy has a dedicated file for testing specific GitHub issues that have been fixed. Each test function is named after the issue number, making it easy to track which issues have tests:

```python
def test_issue_145():
    video = ColorClip((800, 600), color=(255, 0, 0)).with_duration(5)
    with pytest.raises(Exception):
        concatenate_videoclips([video], method="composite")
```

Sources: [tests/test_issues.py:20-23]()

## Testing Complex Functionality

MoviePy includes tests for complex functionality like video compositing, transparency, and effects. These tests often involve creating test clips, applying operations, and verifying the results.

```mermaid
flowchart LR
    subgraph "Clip Creation"
        ColorClip["ColorClip"]
        VideoFileClip["VideoFileClip"]
        ImageClip["ImageClip"]
        BitmapClip["BitmapClip"]
    end
    
    subgraph "Operations"
        Compositing["CompositeVideoClip"]
        Concatenation["concatenate_videoclips"]
        Effects["with_effects"]
        WritingFiles["write_videofile"]
    end
    
    subgraph "Verification"
        PixelTesting["ClipPixelTest"]
        DurationChecks["Duration Assertions"]
        FrameChecks["Frame Assertions"]
        ExceptionChecks["Exception Checks"]
    end
    
    Clip["Test Clip"] --> Operations
    Operations --> Verification
    
    ColorClip --> Clip
    VideoFileClip --> Clip
    ImageClip --> Clip
    BitmapClip --> Clip
```

Sources: [tests/test_compositing.py](), [tests/test_ffmpeg_writer.py](), [tests/test_VideoFileClip.py]()

### Testing Transparency and Compositing

One complex area tested in MoviePy is transparency and compositing. These tests verify that when clips are combined, their transparency is correctly handled:

```python
def test_compositing_masks(util):
    # Has one R 30%, one G 30%, one B 30%
    clip1 = ColorClip((100, 100), (255, 0, 0, 76.5)).with_duration(2)
    clip2 = ColorClip((50, 50), (0, 255, 0, 76.5)).with_duration(2)
    clip3 = ColorClip((25, 25), (0, 0, 255, 76.5)).with_duration(2)

    compostite_clip1 = CompositeVideoClip(
        [clip1, clip2.with_position(("center", "center"))]
    )
    compostite_clip2 = CompositeVideoClip(
        [compostite_clip1, clip3.with_position(("center", "center"))]
    )

    # Load output file and check transparency
    frame = compostite_clip2.mask.get_frame(1)

    # We check opacity with one, two and three layers
    # Allow for a bit of tolerance (about 1%) to account
    # for rounding errors
    opacity1 = frame[50, 10]
    opacity2 = frame[50, 30]
    opacity3 = frame[50, 50]
    assert abs(opacity1 - 0.3) < 0.01
    assert abs(opacity2 - 0.51) < 0.01
    assert abs(opacity3 - 0.657) < 0.01
```

Sources: [tests/test_compositing.py:113-137]()

## Running Tests

Tests in MoviePy can be run using pytest. The main way to run all tests is:

```
pytest
```

To run a specific test file:

```
pytest tests/test_VideoFileClip.py
```

To run a specific test function:

```
pytest tests/test_issues.py::test_issue_407
```

The test framework also supports running with different verbosity levels and generating reports. When tests are run, temporary files are created in the system's temporary directory, as defined in `conftest.py`:

```python
TMP_DIR = tempfile.gettempdir()  # because tempfile.tempdir is sometimes None
```

Sources: [tests/conftest.py:22]()

## Best Practices for MoviePy Tests

Based on the existing tests, here are some best practices for writing tests for MoviePy:

1. **Organize tests by component**: Create separate test files for different components or features.
2. **Use fixtures for common resources**: Utilize fixtures for common test resources like test clips and utilities.
3. **Parameterize when appropriate**: Use pytest's parameterization to test functions with multiple inputs.
4. **Clean up temporary files**: Make sure temporary files are cleaned up after tests.
5. **Test edge cases**: Test both normal operation and edge cases, including error conditions.
6. **Verify pixel values when necessary**: For visual operations, verify the actual pixel values of the resulting clips.
7. **Use reasonable tolerances**: When testing pixel values, use reasonable tolerances to account for slight variations due to compression or rounding.
8. **Document test purpose**: Include comments or docstrings explaining what each test is verifying.

```mermaid
flowchart TD
    subgraph "Test Creation Process"
        Identify["Identify Feature to Test"]
        Setup["Set Up Test Environment"]
        CreateClips["Create Test Clips"]
        PerformOperation["Perform Operation Under Test"]
        VerifyResults["Verify Results"]
        Cleanup["Clean Up"]
    end
    
    Identify --> Setup
    Setup --> CreateClips
    CreateClips --> PerformOperation
    PerformOperation --> VerifyResults
    VerifyResults --> Cleanup
    
    subgraph "Verification Methods"
        AssertDuration["Assert Duration"]
        AssertSize["Assert Size"]
        AssertPixelValues["Assert Pixel Values"]
        AssertException["Assert Exception Raised"]
        AssertFileCreated["Assert File Created"]
    end
    
    VerifyResults --> AssertDuration
    VerifyResults --> AssertSize
    VerifyResults --> AssertPixelValues
    VerifyResults --> AssertException
    VerifyResults --> AssertFileCreated
```

Sources: [tests/test_issues.py](), [tests/test_compositing.py](), [tests/test_VideoFileClip.py](), [tests/test_ffmpeg_writer.py]()

## Documentation Testing

MoviePy includes tests to ensure that examples in the documentation work correctly. The `test_doc_examples.py` file runs Python scripts from the documentation to verify they execute without errors:

```python
@pytest.mark.parametrize("script", scripts)
def test_doc_examples(util, tmp_path, script):
    if os.path.basename(script) == "preview.py":
        pytest.skip("Skipping preview.py because no display is available")
    print("Try script: ", script)

    if os.path.basename(script) in DOC_EXAMPLES_IGNORE:
        return

    # Lets build a test dir with all medias needed to run our test in
    shutil.copytree(util.DOC_EXAMPLES_MEDIAS_DIR, os.path.join(tmp_path, "doc_tests"))
    test_dir = os.path.join(tmp_path, "doc_tests")

    with cwd(test_dir):
        runpy.run_path(script)
```

Sources: [tests/test_doc_examples.py:40-55]()

This approach ensures that the documentation stays up-to-date with the codebase and provides working examples for users.