repos:
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        files: \.py$
  - repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args:
          - '--filter-files'
        files: \.py$
  - repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-absolute-import>=1.0
          - flake8-docstrings>=1.7.0
          - flake8-rst-docstrings>=0.3
          - flake8-implicit-str-concat==0.4.0
        name: flake8-test
        files: \.py$
