#!/usr/bin/env python3
"""
视频生成单元测试框架

这个框架专门为直接输出视频的程序设计，提供：
1. 模块化测试组件
2. 视频质量验证
3. 布局验证
4. 性能测试
5. 回归测试支持
"""

import os
import time
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 导入项目模块
from lyric_timeline import LyricTimeline, LyricDisplayMode
from enhanced_generator import EnhancedJingwuGenerator
from layout_engine import LayoutEngine, VerticalStackStrategy
from layout_types import LyricRect


@dataclass
class TestResult:
    """测试结果数据结构"""
    test_name: str
    success: bool
    duration: float
    output_file: Optional[str] = None
    file_size_mb: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class VideoQualityMetrics:
    """视频质量指标"""
    file_exists: bool
    file_size_mb: float
    duration_sec: Optional[float] = None
    resolution: Optional[Tuple[int, int]] = None
    fps: Optional[float] = None
    has_audio: bool = False


class TestComponent(ABC):
    """测试组件抽象基类"""
    
    @abstractmethod
    def run_test(self) -> TestResult:
        """运行测试"""
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理测试资源"""
        pass


class LayoutValidationTest(TestComponent):
    """布局验证测试组件"""
    
    def __init__(self, chinese_lrc: str, english_lrc: str, 
                 video_width: int = 720, video_height: int = 1280):
        self.chinese_lrc = chinese_lrc
        self.english_lrc = english_lrc
        self.video_width = video_width
        self.video_height = video_height
        self.temp_files = []
    
    def run_test(self) -> TestResult:
        """测试布局验证"""
        start_time = time.time()
        
        try:
            # 创建时间轴
            chinese_timeline = LyricTimeline.from_lrc_file(
                self.chinese_lrc, "chinese", LyricDisplayMode.ENHANCED_PREVIEW
            )
            english_timeline = LyricTimeline.from_lrc_file(
                self.english_lrc, "english", LyricDisplayMode.SIMPLE_FADE
            )
            
            # 测试原始区域重叠检测
            chinese_rect = chinese_timeline.calculate_required_rect(
                self.video_width, self.video_height
            )
            english_rect = english_timeline.calculate_required_rect(
                self.video_width, self.video_height
            )
            
            original_overlaps = chinese_rect.overlaps_with(english_rect)
            
            # 测试布局引擎
            layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
            layout_engine.add_element(chinese_timeline)
            layout_engine.add_element(english_timeline)
            
            # 冲突检测
            conflicts = layout_engine.detect_conflicts(self.video_width, self.video_height)
            
            # 布局计算
            layout_result = layout_engine.calculate_layout(self.video_width, self.video_height)
            
            # 验证布局后无重叠
            layout_rects = list(layout_result.element_positions.values())
            final_overlaps = False
            if len(layout_rects) >= 2:
                final_overlaps = layout_rects[0].overlaps_with(layout_rects[1])
            
            # 计算间距
            gap = 0
            if len(layout_rects) >= 2:
                rect1, rect2 = layout_rects[0], layout_rects[1]
                if rect1.y < rect2.y:
                    gap = rect2.y - (rect1.y + rect1.height)
                else:
                    gap = rect1.y - (rect2.y + rect2.height)
            
            duration = time.time() - start_time
            
            # 验证结果
            success = (
                original_overlaps and  # 原始区域应该重叠
                len(conflicts) > 0 and  # 应该检测到冲突
                not final_overlaps and  # 布局后不应该重叠
                gap >= 20  # 应该有合理的间距
            )
            
            metadata = {
                "original_overlaps": original_overlaps,
                "conflicts_detected": len(conflicts),
                "final_overlaps": final_overlaps,
                "gap_pixels": gap,
                "chinese_rect": str(chinese_rect),
                "english_rect": str(english_rect),
                "layout_rects": [str(rect) for rect in layout_rects]
            }
            
            return TestResult(
                test_name="layout_validation",
                success=success,
                duration=duration,
                metadata=metadata
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_name="layout_validation",
                success=False,
                duration=duration,
                error_message=str(e)
            )
    
    def cleanup(self):
        """清理资源"""
        for temp_file in self.temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)


class VideoGenerationTest(TestComponent):
    """视频生成测试组件"""
    
    def __init__(self, chinese_lrc: str, english_lrc: str, audio_file: str,
                 duration_limit: float = 10.0, video_width: int = 720, video_height: int = 1280):
        self.chinese_lrc = chinese_lrc
        self.english_lrc = english_lrc
        self.audio_file = audio_file
        self.duration_limit = duration_limit
        self.video_width = video_width
        self.video_height = video_height
        self.output_file = None
    
    def run_test(self) -> TestResult:
        """测试视频生成"""
        start_time = time.time()
        
        try:
            # 创建临时输出文件
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp:
                self.output_file = tmp.name
            
            # 创建时间轴
            chinese_timeline = LyricTimeline.from_lrc_file(
                self.chinese_lrc, "chinese", LyricDisplayMode.ENHANCED_PREVIEW
            )
            english_timeline = LyricTimeline.from_lrc_file(
                self.english_lrc, "english", LyricDisplayMode.SIMPLE_FADE
            )
            
            # 创建生成器
            generator = EnhancedJingwuGenerator(
                width=self.video_width, 
                height=self.video_height, 
                fps=24
            )
            
            # 生成视频
            success = generator.generate_bilingual_video(
                main_timeline=chinese_timeline,
                aux_timeline=english_timeline,
                audio_path=self.audio_file,
                output_path=self.output_file,
                background_image=None,
                t_max_sec=self.duration_limit
            )
            
            duration = time.time() - start_time
            
            # 验证输出文件
            file_size_mb = 0
            if success and os.path.exists(self.output_file):
                file_size_mb = os.path.getsize(self.output_file) / (1024 * 1024)
            
            return TestResult(
                test_name="video_generation",
                success=success and file_size_mb > 0,
                duration=duration,
                output_file=self.output_file if success else None,
                file_size_mb=file_size_mb if success else None
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_name="video_generation",
                success=False,
                duration=duration,
                error_message=str(e)
            )
    
    def cleanup(self):
        """清理资源"""
        if self.output_file and os.path.exists(self.output_file):
            os.remove(self.output_file)


class VideoTestSuite:
    """视频测试套件"""
    
    def __init__(self):
        self.tests: List[TestComponent] = []
        self.results: List[TestResult] = []
    
    def add_test(self, test: TestComponent):
        """添加测试组件"""
        self.tests.append(test)
    
    def run_all_tests(self) -> List[TestResult]:
        """运行所有测试"""
        self.results.clear()
        
        print("🧪 开始运行视频生成测试套件")
        print("=" * 60)
        
        for i, test in enumerate(self.tests, 1):
            print(f"\n🔧 运行测试 {i}/{len(self.tests)}: {test.__class__.__name__}")
            
            result = test.run_test()
            self.results.append(result)
            
            # 显示结果
            status = "✅ 通过" if result.success else "❌ 失败"
            print(f"   {status} - 耗时: {result.duration:.2f}s")
            
            if result.output_file:
                print(f"   📁 输出文件: {result.output_file}")
            if result.file_size_mb:
                print(f"   📊 文件大小: {result.file_size_mb:.1f} MB")
            if result.error_message:
                print(f"   ❌ 错误: {result.error_message}")
            
            # 清理测试资源
            test.cleanup()
        
        # 显示总结
        self._print_summary()
        
        return self.results
    
    def _print_summary(self):
        """打印测试总结"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - passed_tests
        total_duration = sum(r.duration for r in self.results)
        
        print(f"\n🎉 测试总结")
        print("=" * 50)
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   总耗时: {total_duration:.2f}s")
        print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")


def create_standard_test_suite() -> VideoTestSuite:
    """创建标准测试套件"""
    suite = VideoTestSuite()
    
    # 检查必要文件
    chinese_lrc = "精武英雄/精武英雄 - 甄子丹.lrc"
    english_lrc = "精武英雄/Jingwu Hero - Donnie Yen.lrc"
    audio_file = "精武英雄/精武英雄 - 甄子丹.flac"
    
    if not all(os.path.exists(f) for f in [chinese_lrc, english_lrc, audio_file]):
        print("❌ 缺少必要的测试文件")
        return suite
    
    # 添加布局验证测试
    suite.add_test(LayoutValidationTest(chinese_lrc, english_lrc))
    
    # 添加视频生成测试
    suite.add_test(VideoGenerationTest(chinese_lrc, english_lrc, audio_file, duration_limit=15.0))
    
    return suite


if __name__ == "__main__":
    # 运行标准测试套件
    suite = create_standard_test_suite()
    results = suite.run_all_tests()
    
    # 检查是否所有测试都通过
    all_passed = all(r.success for r in results)
    exit_code = 0 if all_passed else 1
    
    print(f"\n🏁 测试完成，退出码: {exit_code}")
    exit(exit_code)
