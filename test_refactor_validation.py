#!/usr/bin/env python3
"""
测试职责分离重构的验证脚本

验证_parse_lrc_file和_preprocess_lyrics函数的职责分离重构是否成功
"""

import os
import tempfile
from pathlib import Path
from lyric_timeline import LyricTimeline, LyricDisplayMode

def create_test_lrc_with_empty_lines():
    """创建包含空行和多行歌词的测试LRC文件"""
    lrc_content = """[00:10.00]第一句歌词
[00:15.00]
[00:20.00]第二句歌词
[00:20.00]第二句的第二行
[00:25.00]   
[00:30.00]第三句歌词
[00:30.00]
[00:30.00]第三句的第二行
[00:35.00]第四句歌词
[00:40.00]   空格行   
[00:45.00]最后一句
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.lrc', delete=False, encoding='utf-8') as f:
        f.write(lrc_content)
        return f.name

def test_parse_lrc_file_raw_output():
    """测试_parse_lrc_file的原始输出（应该保留原始文本）"""
    print("🔍 测试_parse_lrc_file的原始输出")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_empty_lines()
    
    try:
        # 直接调用_parse_lrc_file
        raw_lyrics = LyricTimeline._parse_lrc_file(lrc_path)
        
        print(f"✅ 解析得到 {len(raw_lyrics)} 条原始歌词")
        
        for i, (timestamp, text) in enumerate(raw_lyrics):
            lines = text.split('\n')
            print(f"📝 [{timestamp:06.2f}s] 原始文本 ({len(lines)} 行):")
            for j, line in enumerate(lines):
                print(f"    {j+1}: '{line}' (长度: {len(line)})")
        
        return raw_lyrics
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def test_preprocess_lyrics_cleaned_output():
    """测试_preprocess_lyrics的清理输出"""
    print("\n🧹 测试_preprocess_lyrics的清理输出")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_empty_lines()
    
    try:
        # 创建LyricTimeline实例，触发预处理
        timeline = LyricTimeline.from_lrc_file(lrc_path, language="test")
        
        # 获取预处理后的数据
        processed_lyrics = timeline.get_processed_lyrics()
        
        print(f"✅ 预处理得到 {len(processed_lyrics)} 条清理后的歌词")
        
        for i, (timestamp, lines) in enumerate(processed_lyrics):
            print(f"📝 [{timestamp:06.2f}s] 清理后文本 ({len(lines)} 行):")
            for j, line in enumerate(lines):
                print(f"    {j+1}: '{line}' (长度: {len(line)})")
        
        return processed_lyrics
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def test_responsibility_separation():
    """测试职责分离效果"""
    print("\n🎯 测试职责分离效果")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_empty_lines()
    
    try:
        # 1. 测试原始解析（应该保留空行和空格）
        raw_lyrics = LyricTimeline._parse_lrc_file(lrc_path)
        
        # 2. 测试预处理清理（应该移除空行和空格）
        timeline = LyricTimeline.from_lrc_file(lrc_path, language="test")
        processed_lyrics = timeline.get_processed_lyrics()
        
        print("📊 职责分离验证:")
        print(f"   原始解析结果: {len(raw_lyrics)} 条")
        print(f"   预处理结果: {len(processed_lyrics)} 条")
        
        # 验证原始数据包含空行
        has_empty_lines = False
        for timestamp, text in raw_lyrics:
            if '\n\n' in text or text.strip() != text:
                has_empty_lines = True
                break
        
        # 验证预处理数据不包含空行
        has_clean_lines = True
        for timestamp, lines in processed_lyrics:
            for line in lines:
                if not line.strip():
                    has_clean_lines = False
                    break
        
        print(f"   原始数据包含空行/空格: {'✅' if has_empty_lines else '❌'}")
        print(f"   预处理数据完全清理: {'✅' if has_clean_lines else '❌'}")
        
        if has_empty_lines and has_clean_lines:
            print("\n🎉 职责分离重构成功!")
            print("   - _parse_lrc_file: 只负责格式解析，保留原始文本")
            print("   - _preprocess_lyrics: 统一负责文本清理和预处理")
        else:
            print("\n❌ 职责分离重构可能有问题")
        
        return has_empty_lines and has_clean_lines
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_empty_lines()
    
    try:
        # 创建时间轴并生成视频片段（模拟实际使用）
        timeline = LyricTimeline.from_lrc_file(
            lrc_path, 
            language="test",
            display_mode=LyricDisplayMode.SIMPLE_FADE
        )
        
        print(f"✅ 时间轴创建成功: {timeline.get_info()['total_lines']} 条歌词")
        print(f"✅ 最大行数计算: {timeline.max_lines} 行")
        print(f"✅ 预处理数据: {len(timeline.get_processed_lyrics())} 条")
        
        # 测试区域计算
        rect = timeline.calculate_required_rect(1280, 720)
        print(f"✅ 区域计算成功: {rect}")
        
        print("\n🎉 向后兼容性测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def main():
    """主测试函数"""
    print("🧪 职责分离重构验证测试")
    print("=" * 60)
    
    # 运行所有测试
    test_parse_lrc_file_raw_output()
    test_preprocess_lyrics_cleaned_output()
    separation_success = test_responsibility_separation()
    compatibility_success = test_backward_compatibility()
    
    print("\n📋 测试总结")
    print("=" * 60)
    print(f"职责分离重构: {'✅ 成功' if separation_success else '❌ 失败'}")
    print(f"向后兼容性: {'✅ 通过' if compatibility_success else '❌ 失败'}")
    
    if separation_success and compatibility_success:
        print("\n🎉 所有测试通过! 重构成功完成")
        print("💡 现在代码遵循单一职责原则和DRY原则")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
