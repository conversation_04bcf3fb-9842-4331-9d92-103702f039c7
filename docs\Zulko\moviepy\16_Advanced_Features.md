# Advanced Features

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/video/io/ImageSequenceClip.py](moviepy/video/io/ImageSequenceClip.py)
- [moviepy/video/tools/cuts.py](moviepy/video/tools/cuts.py)
- [tests/test_videotools.py](tests/test_videotools.py)

</details>



This document covers the advanced features and techniques in MoviePy that go beyond basic video editing. Here we explore specialized tools for scene detection, frame matching, image sequence processing, and other advanced capabilities that enable sophisticated video analysis and creative effects.

For basic video editing operations, see [Core Architecture](#2) and [Media Processing](#3).

## 1. Scene Detection and Frame Analysis

MoviePy provides powerful tools for analyzing video content, detecting scenes, identifying matching frames, and finding repeating patterns.

### 1.1 Scene Detection

The `detect_scenes` function identifies scene changes in videos by analyzing frame-to-frame luminosity differences:

```mermaid
flowchart TD
    subgraph "Scene Detection Process"
        A["Input Video Clip"] --> B["Extract Frames"]
        B --> C["Calculate Luminosity"]
        C --> D["Detect Luminosity Jumps"]
        D --> E["Identify Scene Cuts"]
        E --> F["Return Scene Timings"]
    end
```

Usage example:
```python
from moviepy import VideoFileClip
from moviepy.video.tools.cuts import detect_scenes

clip = VideoFileClip("myvideo.mp4")
cuts, luminosities = detect_scenes(clip, luminosity_threshold=10)
# cuts is a list of tuples containing start and end times of scenes: [(0,t1), (t1,t2),...(...,tf)]
```

This function is particularly useful for automatically splitting a video into coherent scenes or for detecting abrupt transitions.

Sources: [moviepy/video/tools/cuts.py:462-522]()

### 1.2 Video Period Detection

The `find_video_period` function identifies repeating patterns in videos, which is useful for creating seamless loops:

```mermaid
flowchart TD
    A["Input Video"] --> B["Sample Frames at FPS rate"]
    B --> C["Calculate Frame Correlation"]
    C --> D["Find Peak Correlation"]
    D --> E["Return Period Duration"]
```

Example usage:
```python
from moviepy import VideoFileClip
from moviepy.video.tools.cuts import find_video_period

clip = VideoFileClip("myloop.mp4")
period = find_video_period(clip, fps=60)  # Higher fps = more accurate
print(f"This video loops every {period} seconds")
```

Sources: [moviepy/video/tools/cuts.py:10-47]()

### 1.3 Frame Matching System

The frame matching system allows you to find frames that look similar within a video, which is useful for creating seamless loops, identifying repeated content, or finding specific visual patterns.

```mermaid
classDiagram
    class FramesMatch {
        +start_time
        +end_time
        +min_distance
        +max_distance
        +time_span
    }
    
    class FramesMatches {
        +best()
        +filter()
        +save()
        +load()
        +from_clip()
        +select_scenes()
        +write_gifs()
    }
    
    FramesMatches --* FramesMatch : contains
```

The frame matching system works by comparing frames and finding pairs with visual similarity below a certain threshold:

```mermaid
flowchart TD
    A["Input Video"] --> B["Extract Frames"]
    B --> C["Compare Frame Distances"]
    C --> D["Store Matching Frames"]
    D --> E["Filter by Distance Threshold"]
    E --> F["Select Best Matches"]
    F --> G["Return FramesMatches"]
    
    subgraph "Optional Post-Processing"
        G --> H["Filter by Duration"]
        H --> I["Select Scenes"]
        I --> J["Write GIFs of Matches"]
    end
```

Example usage for finding a loopable segment:

```python
from moviepy import VideoFileClip
from moviepy.video.tools.cuts import FramesMatches

clip = VideoFileClip("video.mp4")
# Find frames that look alike within 3 seconds of each other
matches = FramesMatches.from_clip(clip, distance_threshold=10, max_duration=3)
# Filter for matches with > 1 second duration
good_matches = matches.filter(lambda match: match.time_span > 1)
# Get the best match
best = good_matches.best()
# Create a loop
loop = clip.subclipped(best.start_time, best.end_time)
loop.write_gif("loop.gif")
```

Sources: [moviepy/video/tools/cuts.py:50-460]()

## 2. Image Sequence Processing

### 2.1 Creating Video from Image Sequences

The `ImageSequenceClip` class allows you to create videos from sequences of images, with fine control over timing and presentation:

```mermaid
flowchart TD
    A["Images Source"] --> B["ImageSequenceClip"]
    B --> C["Video Clip"]
    
    subgraph "Images Source Options"
        D["Folder Path"]
        E["List of Image Files"]
        F["List of NumPy Arrays"]
    end
    
    subgraph "Timing Options"
        G["Fixed FPS"]
        H["Custom Durations"]
    end
    
    D --> A
    E --> A
    F --> A
    G --> B
    H --> B
```

The `ImageSequenceClip` can process images from various sources:
- A folder containing image files
- A list of image file paths
- A list of NumPy arrays containing image data

Example usage:

```python
from moviepy import ImageSequenceClip

# From a folder
clip1 = ImageSequenceClip("path/to/images_folder", fps=24)

# From a list of files
file_list = ["img1.jpg", "img2.jpg", "img3.jpg"]
clip2 = ImageSequenceClip(file_list, fps=24)

# With custom durations for each image
durations = [1.5, 2.0, 1.0]  # seconds per image
clip3 = ImageSequenceClip(file_list, durations=durations)
```

The class also supports alpha channels in PNG images, which can be used as masks.

Sources: [moviepy/video/io/ImageSequenceClip.py:1-168]()

## 3. Drawing and Visual Effects Tools

MoviePy provides several advanced tools for creating visual effects and custom graphics.

### 3.1 Color Gradients and Shapes

```mermaid
flowchart TD
    A["Drawing Functions"] --> B["color_gradient()"]
    A --> C["color_split()"]
    A --> D["circle()"]
    
    B --> E["Linear Gradient"]
    B --> F["Bilinear Gradient"]
    B --> G["Radial Gradient"]
    
    C --> H["Horizontal Split"]
    C --> I["Vertical Split"]
    C --> J["Custom Angle Split"]
```

These functions allow you to create custom visual elements:

- `color_gradient`: Creates gradients between two colors (linear, bilinear, or radial)
- `color_split`: Splits an image into two color regions with optional gradient
- `circle`: Creates a circle shape with specified color

Example:
```python
from moviepy.video.tools.drawing import color_gradient, color_split, circle

# Create a linear gradient from red to green
size = (720, 480)
gradient = color_gradient(size, p1=(0, 240), p2=(720, 240), 
                          color_1=(255, 0, 0), color_2=(0, 255, 0))

# Create a split color image
split = color_split(size, x=360, color_1=(255, 0, 0), color_2=(0, 0, 255),
                   gradient_width=50)
```

Sources: [tests/test_videotools.py:622-819]()

### 3.2 Interpolators and Trajectories

MoviePy offers tools for creating smooth animations and movements:

```mermaid
classDiagram
    class Interpolator {
        +ttss or tt+ss
        +left
        +right
        +__call__(t)
    }
    
    class Trajectory {
        +tt
        +xx
        +yy
        +__call__(t)
        +addx()
        +addy()
        +from_file()
        +to_file()
    }
```

- `Interpolator`: Creates a function that interpolates values based on time
- `Trajectory`: Manages movement along a path defined by time points (tt) and positions (xx, yy)

Example:
```python
from moviepy.video.tools.interpolators import Interpolator, Trajectory

# Create a position interpolator
position_interp = Trajectory([0, 1, 2], [20, 50, 10], [30, 80, 40])

# Use in a clip animation
clip = VideoFileClip("myvideo.mp4")
clip_moving = clip.with_position(lambda t: position_interp(t))
```

Sources: [tests/test_videotools.py:822-930]()

## 4. Credits Generation

The `CreditsClip` class provides a specialized tool for creating scrolling credits for videos:

```mermaid
flowchart TD
    A["Credits Text File"] --> B["CreditsClip"]
    B --> C["Credits Video Clip"]
    
    subgraph "Text File Format"
        D["# Comments"]
        E["..Section Titles"]
        F["Names"]
        G[".blank N (blank lines)"]
    end
    
    subgraph "Styling Options"
        H["Font"]
        I["Color"]
        J["Stroke"]
        K["Size"]
    end
    
    D --> A
    E --> A
    F --> A
    G --> A
    H --> B
    I --> B
    J --> B
    K --> B
```

Example credits text file:
```
# This is a comment
..Executive Producer
JOHN SMITH

.blank 2

..Associate Producers
JANE DOE
ROBERT JOHNSON
```

Usage example:
```python
from moviepy.video.tools.credits import CreditsClip

credits = CreditsClip("credits.txt", 720, gap=100, 
                     stroke_color="black", stroke_width=2, 
                     font="Arial")
credits = credits.with_duration(20)  # 20 seconds of credits
final_video = concatenate_videoclips([main_movie, credits])
```

Sources: [tests/test_videotools.py:35-66]()

## 5. Performance Optimization Techniques

### 5.1 Frame Caching

When working with complex effects or large videos, caching can improve performance:

```python
# Create a clip with caching for intensive computations
clip = VideoFileClip("video.mp4")
# Enable frame caching - stores computed frames to avoid recalculation
processed_clip = clip.with_effects(vfx.painting).enable_frame_caching()
```

### 5.2 Working with Long Videos

For efficient processing of long videos:

```mermaid
flowchart TD
    A["Long Video"] --> B["Split into Segments"]
    B --> C["Process Segments"]
    C --> D["Recombine Segments"]
```

Example approach:
```python
from moviepy import VideoFileClip, concatenate_videoclips

# Split a long video into manageable segments
clip = VideoFileClip("long_video.mp4")
duration = clip.duration
segment_length = 60  # 60 seconds per segment

segments = []
for i in range(0, int(duration), segment_length):
    end = min(i + segment_length, duration)
    segment = clip.subclipped(i, end)
    # Process the segment
    processed = segment.with_effects(...)
    segments.append(processed)

# Recombine segments
final_video = concatenate_videoclips(segments)
```

This approach helps manage memory usage and allows for parallel processing of segments.

## 6. Integrating with External Tools

MoviePy can be integrated with other libraries to extend its capabilities:

```mermaid
flowchart TD
    A["MoviePy"] --> B["Computer Vision (OpenCV)"]
    A --> C["Machine Learning (TensorFlow/PyTorch)"]
    A --> D["Audio Processing (Librosa)"]
    A --> E["3D Rendering (Blender)"]
    
    subgraph "Integration Workflow"
        F["Extract Frames/Audio"] --> G["Process with External Tool"]
        G --> H["Reintegrate into MoviePy"]
    end
```

Example with OpenCV:
```python
import cv2
import numpy as np
from moviepy import VideoFileClip

def apply_cv2_effect(image):
    # Convert to OpenCV format if needed
    img = image.copy()
    # Apply OpenCV operations
    gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
    edges = cv2.Canny(gray, 100, 200)
    # Convert back to RGB for MoviePy
    result = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
    return result

clip = VideoFileClip("myvideo.mp4")
# Apply the OpenCV effect to each frame
processed_clip = clip.image_transform(apply_cv2_effect)
```

This architecture allows MoviePy to leverage specialized libraries for tasks like facial recognition, object detection, or advanced audio processing.

Sources: Tests and code across the codebase