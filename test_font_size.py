#!/usr/bin/env python3
"""
测试字体大小功能的脚本
验证LrcInfo中的font_size属性是否正确传递到LyricTimeline
"""

from pathlib import Path
from lrc_mv_config import load_lrc_mv_config
from enhanced_generator import demo_enhanced_features
import tempfile
import yaml

def test_font_size_functionality():
    """测试字体大小功能"""
    print("🔍 测试LrcInfo字体大小功能")
    print("=" * 60)
    
    # 创建测试配置
    test_config = {
        'audio': '精武英雄 - 甄子丹.flac',
        'main-lrc': {
            'path': '精武英雄 - 甄子丹.lrc',
            'lang': 'hans',
            'font_size': 100  # 大字体
        },
        'aux-lrc': {
            'path': 'Jingwu Hero - Donnie Yen.lrc',
            'lang': 'en',
            'font_size': 40   # 小字体
        },
        'background': 'bg_v.png',
        'width': 720,
        'height': 1280,
        'output': 'test_font_size_output.mp4'
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, 
                                   dir='精武英雄', encoding='utf-8') as f:
        yaml.dump(test_config, f, allow_unicode=True, default_flow_style=False)
        temp_config_path = Path(f.name)
    
    try:
        print(f"📝 创建测试配置文件: {temp_config_path}")
        
        # 加载配置并验证
        config = load_lrc_mv_config(str(temp_config_path))
        
        print(f"✅ 配置加载成功")
        print(f"   主歌词字体大小: {config.main_lrc.font_size}")
        print(f"   副歌词字体大小: {config.aux_lrc.font_size}")
        
        # 验证字体大小
        assert config.main_lrc.font_size == 100, f"主歌词字体大小错误: {config.main_lrc.font_size}"
        assert config.aux_lrc.font_size == 40, f"副歌词字体大小错误: {config.aux_lrc.font_size}"
        
        print("✅ 字体大小配置验证通过")
        
        # 生成测试视频（短时间）
        print("\n🎬 生成测试视频...")
        success = demo_enhanced_features(temp_config_path, t_max_sec=10.0)
        
        if success:
            print("✅ 测试视频生成成功！")
            output_path = Path('精武英雄') / 'test_font_size_output.mp4'
            if output_path.exists():
                file_size = output_path.stat().st_size / (1024 * 1024)
                print(f"   输出文件: {output_path}")
                print(f"   文件大小: {file_size:.1f} MB")
            return True
        else:
            print("❌ 测试视频生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if temp_config_path.exists():
            temp_config_path.unlink()
            print(f"🧹 清理临时配置文件: {temp_config_path}")

def test_default_font_size():
    """测试默认字体大小（不指定font_size时）"""
    print("\n🔍 测试默认字体大小功能")
    print("=" * 60)
    
    # 创建不包含font_size的配置
    test_config = {
        'audio': '精武英雄 - 甄子丹.flac',
        'main-lrc': {
            'path': '精武英雄 - 甄子丹.lrc',
            'lang': 'hans'
            # 不指定font_size
        },
        'aux-lrc': {
            'path': 'Jingwu Hero - Donnie Yen.lrc',
            'lang': 'en'
            # 不指定font_size
        },
        'background': 'bg_v.png',
        'width': 720,
        'height': 1280,
        'output': 'test_default_font_size.mp4'
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, 
                                   dir='精武英雄', encoding='utf-8') as f:
        yaml.dump(test_config, f, allow_unicode=True, default_flow_style=False)
        temp_config_path = Path(f.name)
    
    try:
        print(f"📝 创建测试配置文件: {temp_config_path}")
        
        # 加载配置并验证
        config = load_lrc_mv_config(str(temp_config_path))
        
        print(f"✅ 配置加载成功")
        print(f"   主歌词字体大小: {config.main_lrc.font_size} (应为None)")
        print(f"   副歌词字体大小: {config.aux_lrc.font_size} (应为None)")
        
        # 验证默认值
        assert config.main_lrc.font_size is None, f"主歌词字体大小应为None: {config.main_lrc.font_size}"
        assert config.aux_lrc.font_size is None, f"副歌词字体大小应为None: {config.aux_lrc.font_size}"
        
        print("✅ 默认字体大小配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if temp_config_path.exists():
            temp_config_path.unlink()
            print(f"🧹 清理临时配置文件: {temp_config_path}")

if __name__ == "__main__":
    print("🚀 开始字体大小功能测试")
    print("=" * 80)
    
    # 测试1: 自定义字体大小
    test1_result = test_font_size_functionality()
    
    # 测试2: 默认字体大小
    test2_result = test_default_font_size()
    
    print("\n📊 测试结果总结")
    print("=" * 80)
    print(f"✅ 自定义字体大小测试: {'通过' if test1_result else '失败'}")
    print(f"✅ 默认字体大小测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！字体大小功能实现成功！")
    else:
        print("\n❌ 部分测试失败，需要检查实现")
