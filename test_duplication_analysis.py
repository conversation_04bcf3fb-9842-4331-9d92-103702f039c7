#!/usr/bin/env python3
"""
分析重复逻辑的具体测试

专门测试_parse_lrc_file和_preprocess_lyrics中的重复逻辑问题
"""

import os
import tempfile
from pathlib import Path
from lyric_timeline import LyricTimeline

def create_test_lrc_with_multiline_and_spaces():
    """创建包含多行歌词和空格的测试LRC文件"""
    lrc_content = """[00:10.00]第一句歌词
[00:15.00]第二句歌词
[00:15.00]   第二句的第二行   
[00:15.00]
[00:15.00]第二句的第三行
[00:20.00]第三句歌词
[00:20.00]
[00:20.00]   
[00:20.00]第三句的第二行
[00:25.00]   单行带空格   
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.lrc', delete=False, encoding='utf-8') as f:
        f.write(lrc_content)
        return f.name

def analyze_parse_lrc_behavior():
    """分析_parse_lrc_file的行为"""
    print("🔍 分析_parse_lrc_file的行为")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_multiline_and_spaces()
    
    try:
        # 直接调用_parse_lrc_file
        raw_lyrics = LyricTimeline._parse_lrc_file(lrc_path)
        
        print(f"✅ 解析得到 {len(raw_lyrics)} 条原始歌词")
        
        for i, (timestamp, text) in enumerate(raw_lyrics):
            print(f"\n📝 [{timestamp:06.2f}s] 原始文本:")
            print(f"   完整文本: '{text}'")
            print(f"   repr形式: {repr(text)}")
            
            lines = text.split('\n')
            print(f"   分割后 ({len(lines)} 行):")
            for j, line in enumerate(lines):
                print(f"     {j+1}: '{line}' (长度: {len(line)}, strip后: '{line.strip()}')")
        
        return raw_lyrics
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def analyze_preprocess_behavior():
    """分析_preprocess_lyrics的行为"""
    print("\n🧹 分析_preprocess_lyrics的行为")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_multiline_and_spaces()
    
    try:
        # 创建LyricTimeline实例
        timeline = LyricTimeline.from_lrc_file(lrc_path, language="test")
        
        # 获取预处理后的数据
        processed_lyrics = timeline.get_processed_lyrics()
        
        print(f"✅ 预处理得到 {len(processed_lyrics)} 条清理后的歌词")
        
        for i, (timestamp, lines) in enumerate(processed_lyrics):
            print(f"\n📝 [{timestamp:06.2f}s] 预处理后文本:")
            print(f"   行数组: {lines}")
            print(f"   行数组长度: {len(lines)}")
            
            for j, line in enumerate(lines):
                print(f"     {j+1}: '{line}' (长度: {len(line)})")
        
        return processed_lyrics
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def compare_processing_logic():
    """比较两个函数的处理逻辑"""
    print("\n🔍 比较处理逻辑")
    print("=" * 50)
    
    lrc_path = create_test_lrc_with_multiline_and_spaces()
    
    try:
        # 1. 获取原始解析结果
        raw_lyrics = LyricTimeline._parse_lrc_file(lrc_path)
        
        # 2. 手动模拟_preprocess_lyrics的逻辑
        print("🔧 手动模拟_preprocess_lyrics逻辑:")
        manual_processed = []
        for timestamp, text in raw_lyrics:
            lines = text.split('\n')
            cleaned_lines = [line.strip() for line in lines if line.strip()]
            if cleaned_lines:
                manual_processed.append((timestamp, cleaned_lines))
                print(f"   [{timestamp:06.2f}s] 原始: {repr(text)}")
                print(f"   [{timestamp:06.2f}s] 清理: {cleaned_lines}")
        
        # 3. 获取实际的预处理结果
        timeline = LyricTimeline.from_lrc_file(lrc_path, language="test")
        actual_processed = timeline.get_processed_lyrics()
        
        print(f"\n📊 比较结果:")
        print(f"   手动模拟结果: {len(manual_processed)} 条")
        print(f"   实际预处理结果: {len(actual_processed)} 条")
        
        # 验证结果是否一致
        results_match = len(manual_processed) == len(actual_processed)
        if results_match:
            for i, ((t1, lines1), (t2, lines2)) in enumerate(zip(manual_processed, actual_processed)):
                if t1 != t2 or lines1 != lines2:
                    results_match = False
                    break
        
        print(f"   结果一致性: {'✅' if results_match else '❌'}")
        
        if results_match:
            print("\n✅ 确认存在重复逻辑:")
            print("   - _parse_lrc_file: 不应该进行文本清理")
            print("   - _preprocess_lyrics: 应该统一负责文本清理")
            print("   - 当前两个函数都在执行相同的清理逻辑")
        
        return results_match
        
    finally:
        if os.path.exists(lrc_path):
            os.unlink(lrc_path)

def test_current_duplication():
    """测试当前的重复逻辑问题"""
    print("\n⚠️  测试当前重复逻辑问题")
    print("=" * 50)
    
    # 创建包含需要清理内容的测试数据
    test_lyrics = [
        (10.0, "第一句\n   \n第二行"),  # 包含空行
        (15.0, "   带空格的行   \n另一行"),  # 包含前后空格
        (20.0, "正常行")
    ]
    
    # 创建时间轴，这会触发_preprocess_lyrics
    timeline = LyricTimeline(test_lyrics, language="test")
    
    print("📊 原始数据:")
    for timestamp, text in test_lyrics:
        print(f"   [{timestamp:06.2f}s] '{text}' -> {repr(text)}")
    
    print("\n📊 预处理后数据:")
    processed = timeline.get_processed_lyrics()
    for timestamp, lines in processed:
        print(f"   [{timestamp:06.2f}s] {lines}")
    
    # 检查是否正确清理了空行和空格
    has_proper_cleaning = True
    for timestamp, lines in processed:
        for line in lines:
            if not line.strip() or line != line.strip():
                has_proper_cleaning = False
                break
    
    print(f"\n✅ 预处理清理效果: {'正确' if has_proper_cleaning else '有问题'}")
    
    return has_proper_cleaning

def main():
    """主测试函数"""
    print("🔍 重复逻辑分析测试")
    print("=" * 60)
    
    # 分析当前行为
    analyze_parse_lrc_behavior()
    analyze_preprocess_behavior()
    logic_comparison = compare_processing_logic()
    cleaning_works = test_current_duplication()
    
    print("\n📋 分析总结")
    print("=" * 60)
    print(f"重复逻辑确认: {'✅ 存在' if logic_comparison else '❌ 不存在'}")
    print(f"清理功能正常: {'✅ 正常' if cleaning_works else '❌ 异常'}")
    
    if logic_comparison:
        print("\n💡 重构建议:")
        print("   1. _parse_lrc_file应该只负责LRC格式解析")
        print("   2. _preprocess_lyrics应该统一负责文本清理")
        print("   3. 避免在两个地方重复执行相同的清理逻辑")
    
    print("\n🎯 重构目标:")
    print("   - 遵循单一职责原则")
    print("   - 消除重复代码(DRY原则)")
    print("   - 提高代码可维护性")

if __name__ == "__main__":
    main()
