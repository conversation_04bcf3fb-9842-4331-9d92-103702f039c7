# Contributing

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [.github/ISSUE_TEMPLATE/bug-report.md](.github/ISSUE_TEMPLATE/bug-report.md)
- [.github/ISSUE_TEMPLATE/feature-request.md](.github/ISSUE_TEMPLATE/feature-request.md)
- [.github/ISSUE_TEMPLATE/question.md](.github/ISSUE_TEMPLATE/question.md)
- [.github/PULL_REQUEST_TEMPLATE.md](.github/PULL_REQUEST_TEMPLATE.md)
- [CONTRIBUTING.md](CONTRIBUTING.md)

</details>



This page outlines the process and guidelines for contributing to the MoviePy project. It provides detailed instructions for developers interested in fixing bugs, adding features, or improving documentation.

For information about the testing framework specifically, see [Testing Framework](#6.2). For details about the continuous integration and deployment process, see [CI/CD Workflow](#6.3).

## Setting Up the Development Environment

Before you can contribute to MoviePy, you need to set up a proper development environment.

```mermaid
flowchart TD
    A["Fork MoviePy Repository"] --> B["Clone Your Fork"]
    B --> C["Add Upstream Remote"]
    C --> D["Install Dependencies"]
    D --> E["Configure Pre-commit Hooks"]
    E --> F["Ready for Development"]
    
    A --"GitHub UI"--> A1["github.com/Zulko/moviepy → Your Account"]
    B --"git clone"--> B1["git clone URL_TO_YOUR_FORK"]
    C --"git remote add"--> C1["git remote add upstream github.com/Zulko/moviepy.git"]
    D --"pip install"--> D1["pip install -e \".[optional,doc,test,lint]\""]
    E --"pre-commit"--> E1["pre-commit install"]
```

### 1. Fork the Repository

Fork the official MoviePy repository to your own GitHub account by clicking the "Fork" button on the [official MoviePy repository](https://github.com/Zulko/moviepy).

### 2. Clone Your Fork

Clone your fork to your local machine:

```
git clone URL_TO_YOUR_FORK
```

You can get the URL by clicking the green "Code" button on your forked repository.

### 3. Add Upstream Remote

Add the official MoviePy repository as a second remote called `upstream`:

```
git remote <NAME_EMAIL>:Zulko/moviepy.git
```

or using HTTPS:

```
git remote add upstream https://github.com/Zulko/moviepy.git
```

### 4. Install Dependencies

Install the library with all dependencies in a virtual environment:

```
pip install -e ".[optional,doc,test,lint]"
```

### 5. Configure Pre-commit Hooks

Set up pre-commit hooks to automatically check code quality:

```
pre-commit install
```

Sources: [CONTRIBUTING.md:10-20]()

## Coding Standards and Conventions

MoviePy follows specific coding standards to maintain code quality and consistency.

```mermaid
graph TD
    subgraph "CodeQualityStandards"
        A["PEP8 Compliance"] 
        B["Clear, Minimal Comments"]
        C["Explicit Variable Names"]
        D["Proper Documentation"]
        E["Pre-commit Checks"]
    end
    
    E --> E1["Black Formatter"]
    E --> E2["Flake8 Linter"]
    E --> E3["isort Import Sorter"]
    
    subgraph "DocumentationStandards"
        F["Docstrings for New Functionality"]
        G["Code Comments for Complex Logic"]
        H["Self-documenting Code"]
    end
```

### PEP8 Compliance

All contributions should follow [PEP8](https://www.python.org/dev/peps/pep-0008/) conventions for Python code style.

### Documentation

- Write self-documenting code with explicit variable names
- Add just the right amount of comments - not too many, not too few
- Document new functionality or bug fixes in docstrings or with code comments

### Code Quality Tools

MoviePy uses pre-commit hooks with the following tools:
- Black - for code formatting
- Flake8 - for style guide enforcement
- isort - for organizing imports

Sources: [CONTRIBUTING.md:22-27]()

## Contribution Workflow

The standard workflow for contributing to MoviePy follows these steps:

```mermaid
flowchart TD
    A["Keep Local Master Updated"] --> B["Create Feature Branch"]
    B --> C["Develop Changes"]
    C --> D["Run Tests"]
    D --> E["Push Branch to Fork"]
    E --> F["Create Pull Request"]
    F --> G["Address Review Feedback"]
    G --> H["PR Merged"]
    
    C -- "Periodically" --> I["Rebase on Master"]
    I --> C
    
    A --"git pull upstream master"--> A1["Synced local master"]
    B --"git checkout --branch feature_name"--> B1["New branch created"]
    D --"pytest"--> D1["All tests passing"]
    E --"git push origin feature_name"--> E1["Branch on GitHub fork"]
    I --"git rebase master"--> I1["Up-to-date with official repo"]
```

### Local Development

1. **Keep your local master up-to-date**:
   ```
   git pull upstream master
   ```

2. **Create a feature branch**:
   ```
   git checkout --branch YOUR_DEVELOP_BRANCH
   ```
   
   Use descriptive branch names prefixed with `fix_` (for bug fixes) or `feature_` for new features.

3. **Make your changes** on this branch, making sure they're based on the most recent master.

4. **Rebase regularly** to stay up-to-date with the official repository:
   ```
   git rebase master
   ```

Sources: [CONTRIBUTING.md:30-46]()

## Submitting Pull Requests

When your changes are ready for review, submit a pull request to the official repository.

```mermaid
graph TD
    A["Run Test Suite"] --> B["Push Branch to Fork"]
    B --> C["Create Pull Request on GitHub"]
    C --> D["Fill PR Template"]
    D --> E["Automated Tests Run"]
    E --> F["Maintainer Reviews"]
    F --> G["Address Feedback"]
    G --> H["PR Merged"]
    
    subgraph "PRTemplateChecklist"
        I["Demonstrate Bug/Feature"] 
        J["Add Tests"]
        K["Update Documentation"]
        L["Document Complex Code"]
    end
    
    D --> I
    D --> J
    D --> K
    D --> L
```

### PR Submission Process

1. **Run the test suite** to ensure your changes work correctly:
   ```
   pytest
   ```

2. **Push your branch** to your GitHub fork:
   ```
   git push origin YOUR_DEVELOP_BRANCH
   ```

3. **Create a pull request** on GitHub from your branch to the official `Zulko/moviepy` repository's master branch.

4. **Fill out the PR template** that appears, including:
   - Demonstration of the bug fix or feature
   - Added tests
   - Updated documentation
   - Explanation of complex code

5. **Wait for automated tests** to run on your submission.

6. **Address any feedback** from maintainers during the review process.

You don't need to wait until your feature is complete before opening a PR. You can submit a work-in-progress PR and mention that it's still in development.

Sources: [CONTRIBUTING.md:47-59](), [.github/PULL_REQUEST_TEMPLATE.md:1-8]()

## Communication Guidelines

When participating in the MoviePy project, follow these communication guidelines:

### GitHub Communication

- Keep messages on issues and pull requests on-topic and to the point
- Be aware that each comment sends notifications to multiple people
- Opinions are welcome, but remain constructive

### Complex Discussions

For longer or more in-depth discussions:
- Use the [MoviePy Gitter](https://gitter.im/movie-py/Lobby)
- Summarize discussion outcomes on relevant GitHub issues to document decisions

### API Changes

Do not push any commit that changes the API without prior discussion with the maintainers.

Sources: [CONTRIBUTING.md:3-8]()

## Bug Reporting and Feature Requests

When reporting bugs or requesting features, provide clear and complete information.

```mermaid
graph TD
    subgraph "BugReportComponents"
        A["Expected Behavior"] 
        B["Actual Behavior"]
        C["Reproduction Steps"]
        D["Code Example"]
        E["Media Files"]
        F["Environment Specifications"]
    end
    
    F --> F1["Python Version"]
    F --> F2["MoviePy Version"]
    F --> F3["Platform Name/Version"]
    
    subgraph "FeatureRequestComponents"
        G["Problem Description"] 
        H["Proposed Solution"]
        I["Alternatives Considered"]
    end
```

### Bug Reports

When filing a bug report, include:

1. **Expected behavior** - what should happen?
2. **Actual behavior** - what actually happens?
3. **Steps to reproduce** - how can someone else see this issue?
4. **Complete code example** that reproduces the issue
5. **Media files** needed to reproduce the issue
6. **Specifications**:
   - Python version
   - MoviePy version
   - Platform name and version

### Feature Requests

For feature requests, describe:
1. The problem you're trying to solve
2. Your proposed solution
3. Any alternative solutions you've considered

### Questions

For general questions about using MoviePy, use:
- [GitHub Discussions](https://github.com/Zulko/moviepy/discussions)
- Online forums

Reserve GitHub issues for unexpected behaviors that might be bugs.

Sources: [.github/ISSUE_TEMPLATE/bug-report.md:1-47](), [.github/ISSUE_TEMPLATE/feature-request.md:1-11](), [.github/ISSUE_TEMPLATE/question.md:1-22]()

## MoviePy Project Structure for Contributors

Understanding the project structure is essential for effective contributions. This diagram shows the key components where you might need to make changes:

```mermaid
graph TD
    subgraph "MoviePyRepository"
        Root["moviepy/"] 
        MoviePyInit["__init__.py"]
        Version["version.py"]
        
        subgraph "CoreComponents"
            VideoRoot["video/"] 
            AudioRoot["audio/"]
            
            VideoRoot --> VideoClipPy["VideoClip.py"]
            VideoRoot --> VideoFX["fx/"]
            VideoRoot --> VideoIO["io/"]
            VideoRoot --> Compositing["compositing/"]
            
            AudioRoot --> AudioClipPy["AudioClip.py"]
            AudioRoot --> AudioFX["fx/"]
            AudioRoot --> AudioIO["io/"]
        end
        
        subgraph "DeveloperTools"
            Tests["tests/"] 
            Docs["docs/"]
            PreCommit[".pre-commit-config.yaml"]
            Contributing["CONTRIBUTING.md"]
        end
        
        Tests --> TestVideoClip["test_VideoClip.py"]
        Tests --> TestCompositing["test_compositing.py"]
        Tests --> TestFFmpeg["test_ffmpeg_writer.py"]
    end
    
    ContributorCode["Your Code Changes"] --> CoreComponents
    ContributorTests["Your Tests"] --> Tests
    ContributorDocs["Your Documentation"] --> Docs
```

When contributing to MoviePy, you'll typically interact with:

1. **Core Components** - The main functionality is split between video and audio modules
2. **Tests** - You should add or modify tests that verify your changes
3. **Documentation** - Update docstrings and documentation as needed

Sources: Project Structure and Development Workflow diagram

## Development Tools Integration

This diagram shows how the development tools integrate with the MoviePy contribution process:

```mermaid
flowchart LR
    subgraph "ContributionProcess"
        A["Code Changes"] 
        B["Local Testing"]
        C["Code Quality Checks"]
        D["Push Changes"]
        E["CI/CD Pipeline"]
    end
    
    subgraph "DevelopmentTools"
        F["pytest"] 
        G["pre-commit"]
        H["GitHub Actions"]
    end
    
    G --> G1["black"]
    G --> G2["flake8"]
    G --> G3["isort"]
    
    A --> B
    B --> C
    C --> D
    D --> E
    
    B --> F
    C --> G
    E --> H
```

The contribution process is supported by several tools:

1. **pytest** - Runs the test suite to verify your changes
2. **pre-commit** - Automatically checks code quality using:
   - black - Formats code to a consistent style
   - flake8 - Checks for PEP8 compliance and other issues
   - isort - Organizes imports in a consistent way
3. **GitHub Actions** - Runs automated tests and checks when you submit a PR

Sources: [CONTRIBUTING.md:20-27](), [CONTRIBUTING.md:50-54]()