.. _user_guide:


The MoviePy User Guide
------------------------------

The User Guide covers all of MoviePy's main concepts grouped by tasks (loading, editing, composing, rendering), with a presentation of the different concept/elements relative to the tasks along with short code example.

It is a good place for users wishing to understand more precisely one of these aspects and to discover the different MoviePy elements relative to it.

For users wanting to have a quick overview of how to use MoviePy, a better place to start is the :ref:`getting_started` section, and more specifically the :ref:`moviepy_10_minutes` tutorial.

For a full overview of MoviePy, see the :ref:`reference_manual`.

.. toctree::
   :maxdepth: 1
   
   loading
   modifying
   create_effects
   compositing
   rendering
