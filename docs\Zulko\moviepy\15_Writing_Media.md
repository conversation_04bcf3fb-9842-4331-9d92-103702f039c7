# Writing Media

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [moviepy/audio/io/ffmpeg_audiowriter.py](moviepy/audio/io/ffmpeg_audiowriter.py)
- [moviepy/audio/io/readers.py](moviepy/audio/io/readers.py)
- [moviepy/config.py](moviepy/config.py)
- [moviepy/tools.py](moviepy/tools.py)
- [moviepy/video/io/ffmpeg_writer.py](moviepy/video/io/ffmpeg_writer.py)
- [moviepy/video/io/gif_writers.py](moviepy/video/io/gif_writers.py)

</details>



## Purpose and Scope

This document explains MoviePy's systems for exporting clips (video, audio, images, and GIFs) to various file formats. The writing system primarily leverages FFmpeg, with ImageIO integration for GIF creation. For information about reading media files into MoviePy, see [Reading Media](#4.2).

## Media Writing Architecture Overview

MoviePy uses specialized writer classes and helper functions to create different types of media files:

```mermaid
graph TD
    subgraph "Input"
        VideoClip["VideoClip"]
        AudioClip["AudioClip"]
    end
    
    subgraph "Write Methods"
        VideoWriteMethod["write_videofile()"]
        AudioWriteMethod["write_audiofile()"] 
        ImageWriteMethod["save_frame()/write_images_sequence()"]
        GifWriteMethod["write_gif()"]
    end
    
    subgraph "Writer Components"
        VideoWriter["FFMPEG_VideoWriter"]
        AudioWriter["FFMPEG_AudioWriter"]
        ImageWriter["ffmpeg_write_image()"]
        GifWriter["write_gif_with_imageio()"]
    end
    
    subgraph "External Tools"
        FFmpeg["FFmpeg"]
        ImageIO["ImageIO"]
    end
    
    subgraph "Output Files"
        VideoOutput[".mp4, .webm, etc."]
        AudioOutput[".mp3, .wav, etc."]
        ImageOutput[".jpg, .png, etc."]
        GifOutput[".gif"]
    end
    
    VideoClip --> VideoWriteMethod
    VideoClip --> GifWriteMethod
    VideoClip --> ImageWriteMethod
    AudioClip --> AudioWriteMethod
    
    VideoWriteMethod --> VideoWriter
    AudioWriteMethod --> AudioWriter
    ImageWriteMethod --> ImageWriter
    GifWriteMethod --> GifWriter
    
    VideoWriter --> FFmpeg
    AudioWriter --> FFmpeg
    ImageWriter --> FFmpeg
    GifWriter --> ImageIO
    
    FFmpeg --> VideoOutput
    FFmpeg --> AudioOutput
    FFmpeg --> ImageOutput
    ImageIO --> GifOutput
```

Sources:
- [moviepy/video/io/ffmpeg_writer.py:15-240]()
- [moviepy/audio/io/ffmpeg_audiowriter.py:12-184]()
- [moviepy/video/io/gif_writers.py:9-21]()

## Video Writing

### FFMPEG_VideoWriter Class

The `FFMPEG_VideoWriter` class provides the core functionality for writing video frames to a file using FFmpeg. It handles:

- Setting up the FFmpeg subprocess with appropriate parameters
- Writing individual frames to the subprocess
- Error handling and reporting
- Cleanup when writing is complete

```mermaid
sequenceDiagram
    participant VC as "VideoClip"
    participant FWV as "ffmpeg_write_video()"
    participant FVID as "FFMPEG_VideoWriter"
    participant FF as "FFmpeg"
    
    VC->>FWV: Call with clip, filename, params
    FWV->>FVID: Initialize writer
    FVID->>FF: Start FFmpeg subprocess
    
    loop For each frame
        VC->>VC: get_frame(t)
        FWV->>VC: Request frame
        VC->>FWV: Return frame
        FWV->>FVID: write_frame(frame)
        FVID->>FF: Write raw frame data
    end
    
    FWV->>FVID: close()
    FVID->>FF: Finalize video file
```

Sources:
- [moviepy/video/io/ffmpeg_writer.py:15-240]()
- [moviepy/video/io/ffmpeg_writer.py:242-299]()

### Video Writing Parameters

The video writing system supports numerous parameters for customization:

| Parameter | Description | Default |
|-----------|-------------|---------|
| filename | Output file path | Required |
| fps | Frames per second | Required |
| codec | Video codec (e.g., 'libx264') | 'libx264' |
| bitrate | Video bitrate (e.g., '5000k') | None |
| preset | Encoding speed/quality tradeoff | 'medium' |
| audiofile | Optional audio file to include | None |
| audio_codec | Audio codec for the output | None (use 'copy' if audiofile provided) |
| threads | Number of encoding threads | None |
| ffmpeg_params | Additional FFmpeg parameters | None |
| pixel_format | Output pixel format | Automatic ('rgb24' or 'rgba') |

Sources:
- [moviepy/video/io/ffmpeg_writer.py:15-78]()
- [moviepy/video/io/ffmpeg_writer.py:242-256]()

### Error Handling

The video writer provides detailed error messages for common issues:

- Unknown codec errors
- Format/codec compatibility problems
- Bitrate specification issues
- Invalid encoder types

These error messages include suggestions for resolving the issues, making debugging easier for users.

Sources:
- [moviepy/video/io/ffmpeg_writer.py:168-221]()

## Audio Writing

### FFMPEG_AudioWriter Class

The `FFMPEG_AudioWriter` class manages writing audio data to files through FFmpeg:

- Sets up the FFmpeg subprocess with audio-specific parameters
- Writes chunks of audio data
- Handles errors with detailed messages
- Provides context manager interface for resource cleanup

```mermaid
sequenceDiagram
    participant AC as "AudioClip"
    participant FAW as "ffmpeg_audiowrite()"
    participant FAUD as "FFMPEG_AudioWriter"
    participant FF as "FFmpeg"
    
    AC->>FAW: Call with clip, filename, params
    FAW->>FAUD: Initialize writer
    FAUD->>FF: Start FFmpeg subprocess
    
    loop For each audio chunk
        AC->>AC: iter_chunks()
        FAW->>AC: Request chunk
        AC->>FAW: Return audio chunk
        FAW->>FAUD: write_frames(chunk)
        FAUD->>FF: Write raw audio data
    end
    
    FAW->>FAUD: close()
    FAUD->>FF: Finalize audio file
```

Sources:
- [moviepy/audio/io/ffmpeg_audiowriter.py:12-184]()
- [moviepy/audio/io/ffmpeg_audiowriter.py:186-229]()

### Audio Writing Parameters

Key parameters for audio writing include:

| Parameter | Description | Default |
|-----------|-------------|---------|
| filename | Output file path | Required |
| fps_input | Audio sample rate | Required |
| nbytes | Number of bytes per sample | 2 (16-bit) |
| nchannels | Number of audio channels | 2 (stereo) |
| codec | Audio codec (e.g., 'libmp3lame') | 'libfdk_aac' |
| bitrate | Audio bitrate | None |
| input_video | Optional video file to include | None |
| ffmpeg_params | Additional FFmpeg parameters | None |
| buffersize | Size of buffer for audio chunks | Varies |

Sources:
- [moviepy/audio/io/ffmpeg_audiowriter.py:16-51]()
- [moviepy/audio/io/ffmpeg_audiowriter.py:186-198]()

## Image Writing

MoviePy can write single frames as image files using the `ffmpeg_write_image` function:

```mermaid
flowchart TD
    A["Image Array (NumPy)"] --> B["ffmpeg_write_image()"]
    B --> C["Configure FFmpeg"]
    C --> D["FFmpeg Process"]
    D --> E["Image File"]
```

The function supports:
- Various image formats (PNG, JPEG, BMP, TIFF)
- Alpha channel for transparent images
- Custom pixel formats

Sources:
- [moviepy/video/io/ffmpeg_writer.py:302-363]()

## GIF Writing

For GIF creation, MoviePy uses ImageIO (which calls FreeImage) instead of FFmpeg:

```mermaid
flowchart TD
    A["VideoClip"] --> B["write_gif_with_imageio()"]
    B --> C["ImageIO Writer"]
    C --> D["Frame-by-Frame Processing"]
    D --> E["GIF File"]
```

The GIF writer supports:
- Custom frame rates
- Loop count specification
- Progress reporting

Sources:
- [moviepy/video/io/gif_writers.py:9-21]()

## FFmpeg Integration

MoviePy's media writing relies on FFmpeg, with the following integration points:

### Configuration and Detection

MoviePy automatically configures the FFmpeg binary path by:
1. Checking environment variables
2. Using ImageIO's FFmpeg binary if available
3. Auto-detecting system-installed FFmpeg
4. Falling back to "ffmpeg" command if available

Sources:
- [moviepy/config.py:10-91]()

### Cross-Platform Support

The system includes utilities to ensure FFmpeg works consistently across platforms:
- `cross_platform_popen_params()` adds necessary platform-specific parameters
- `ffmpeg_escape_filename()` ensures filenames are properly escaped for FFmpeg

Sources:
- [moviepy/tools.py:14-24]()
- [moviepy/tools.py:53-61]()

## Supported Formats and Codecs

MoviePy supports a wide range of media formats through its FFmpeg integration:

### Video Formats
- MP4 (H.264, MPEG-4)
- WebM (VP8/VP9)
- MOV (QuickTime)
- AVI (various codecs)
- OGV (Theora)

### Audio Formats
- MP3 (MPEG Layer 3)
- WAV (PCM)
- OGG (Vorbis)
- AAC
- FLAC

### Image Formats
- PNG, JPEG, BMP, TIFF

The `extensions_dict` in the tools module defines these formats and their default codecs.

Sources:
- [moviepy/tools.py:148-163]()
- [moviepy/tools.py:166-187]()

## Media Writing Process

Here's a detailed look at the overall media writing workflow:

```mermaid
classDiagram
    class VideoClip {
        +write_videofile()
        +write_gif()
        +save_frame()
        +write_images_sequence()
    }
    
    class AudioClip {
        +write_audiofile()
    }
    
    class FFMPEG_VideoWriter {
        +__init__(filename, size, fps, codec, ...)
        +write_frame(img_array)
        +close()
    }
    
    class FFMPEG_AudioWriter {
        +__init__(filename, fps_input, ...)
        +write_frames(frames_array)
        +close()
    }
    
    class ffmpeg_write_video {
        +function(clip, filename, fps, ...)
    }
    
    class ffmpeg_audiowrite {
        +function(clip, filename, fps, ...)
    }
    
    class ffmpeg_write_image {
        +function(filename, image, ...)
    }
    
    class write_gif_with_imageio {
        +function(clip, filename, fps, ...)
    }
    
    VideoClip --> ffmpeg_write_video : calls
    VideoClip --> write_gif_with_imageio : calls
    VideoClip --> ffmpeg_write_image : calls
    
    AudioClip --> ffmpeg_audiowrite : calls
    
    ffmpeg_write_video --> FFMPEG_VideoWriter : instantiates
    ffmpeg_audiowrite --> FFMPEG_AudioWriter : instantiates
```

Sources:
- [moviepy/video/io/ffmpeg_writer.py:15-363]()
- [moviepy/audio/io/ffmpeg_audiowriter.py:12-229]()
- [moviepy/video/io/gif_writers.py:9-21]()

## Progress Reporting

All media writing functions use the proglog library to provide progress feedback during encoding:
- Default progress bar display in console
- Custom logger support
- Frame count and time remaining estimation

Sources:
- [moviepy/video/io/ffmpeg_writer.py:260-299]()
- [moviepy/audio/io/ffmpeg_audiowriter.py:207-229]()
- [moviepy/video/io/gif_writers.py:11-20]()
- [moviepy/tools.py:27-51]()