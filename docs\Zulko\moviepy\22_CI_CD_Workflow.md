# CI/CD Workflow

<details>
<summary>Relevant source files</summary>

The following files were used as context for generating this wiki page:

- [.github/workflows/codeql-analysis.yml](.github/workflows/codeql-analysis.yml)
- [.github/workflows/test_suite.yml](.github/workflows/test_suite.yml)
- [.pre-commit-config.yaml](.pre-commit-config.yaml)
- [appveyor.yml](appveyor.yml)
- [moviepy/video/fx/Blink.py](moviepy/video/fx/Blink.py)
- [moviepy/video/fx/Crop.py](moviepy/video/fx/Crop.py)
- [moviepy/video/fx/FreezeRegion.py](moviepy/video/fx/FreezeRegion.py)
- [moviepy/video/fx/MakeLoopable.py](moviepy/video/fx/MakeLoopable.py)
- [moviepy/video/fx/Margin.py](moviepy/video/fx/Margin.py)
- [moviepy/video/fx/Resize.py](moviepy/video/fx/Resize.py)
- [moviepy/video/fx/Rotate.py](moviepy/video/fx/Rotate.py)
- [moviepy/video/io/ffplay_previewer.py](moviepy/video/io/ffplay_previewer.py)
- [tests/__init__.py](tests/__init__.py)

</details>



This document details the Continuous Integration and Continuous Deployment (CI/CD) workflow used in the MoviePy project. It covers the automated testing, code quality checks, and security scanning processes that ensure code reliability and maintainability.

For information about the project's testing framework, see [Testing Framework](#6.2).

## 1. Overview

MoviePy employs a comprehensive CI/CD pipeline powered by GitHub Actions and pre-commit hooks to ensure code quality and functionality across multiple platforms and Python versions.

```mermaid
flowchart TD
    subgraph "Developer Workflow"
        LocalDev["Local Development"]
        PreCommit["Pre-commit Hooks"]
        PullRequest["Pull Request"]
    end

    subgraph "CI/CD Workflow"
        GHActions["GitHub Actions"]
        CodeQL["CodeQL Analysis"]
        TestSuite["Test Suite"]
    end

    subgraph "Test Platforms"
        MacOS["macOS Tests"]
        Windows["Windows Tests"]
        Linux["Linux Tests"]
    end

    LocalDev --> PreCommit
    PreCommit --> PullRequest
    PullRequest --> GHActions
    GHActions --> TestSuite
    GHActions --> CodeQL
    TestSuite --> MacOS
    TestSuite --> Windows
    TestSuite --> Linux
```

Sources: [.github/workflows/test_suite.yml:1-129](), [.github/workflows/codeql-analysis.yml:1-57](), [.pre-commit-config.yaml:1-26]()

## 2. GitHub Actions Configuration

MoviePy uses GitHub Actions to automate testing across multiple platforms (macOS, Windows, Linux) and Python versions (3.9, 3.10, 3.11).

### 2.1 Workflow Triggers

The test suite workflow is triggered on:
- Push events to the master branch
- Pull request events to any branch

```mermaid
flowchart LR
    Push["Push to master"] --> Trigger["Workflow Trigger"]
    PR["Pull Request"] --> Trigger
    Trigger --> Jobs["CI Jobs"]
    Jobs --> MacOS["macos job"]
    Jobs --> Windows["windows job"]
    Jobs --> Linux["linux job"]
```

Sources: [.github/workflows/test_suite.yml:8-12]()

### 2.2 Platform-Specific Jobs

The test suite is configured to run on three different platforms, each with its specific setup:

#### 2.2.1 macOS Configuration

The macOS job:
- Runs on macOS-13
- Uses Miniconda for Python environment management
- Tests against Python 3.9, 3.10, and 3.11
- Uses `pythonw` for testing to support GUI-related functionality

```mermaid
flowchart TD
    macOS["macos job"] --> Setup["Setup miniconda"]
    Setup --> InstallDeps["Install dependencies"]
    InstallDeps --> RunTests["Run tests with pythonw"]
    
    subgraph "Test Environment"
        Miniconda["conda-incubator/setup-miniconda@v3"]
        PythonApp["python.app package"]
        PkgConfig["pkg-config (brew)"]
    end
    
    Setup --> Miniconda
    InstallDeps --> PkgConfig
    InstallDeps --> PythonApp
```

Sources: [.github/workflows/test_suite.yml:16-55]()

#### 2.2.2 Windows Configuration

The Windows job:
- Runs on Windows latest
- Uses standard Python setup from actions/setup-python
- Tests against Python 3.9, 3.10, and 3.11
- Specifically checks for third-party dependencies

```mermaid
flowchart TD
    Windows["windows job"] --> Setup["Setup Python"]
    Setup --> InstallDeps["Install dependencies"]
    InstallDeps --> CheckDeps["Check third-party dependencies"]
    CheckDeps --> RunTests["Run tests"]
    
    subgraph "Windows Setup"
        SetupPython["actions/setup-python@v5.1.0"]
        ConfigCheck["config.py check"]
    end
    
    Setup --> SetupPython
    CheckDeps --> ConfigCheck
```

Sources: [.github/workflows/test_suite.yml:57-82]()

#### 2.2.3 Linux Configuration

The Linux job:
- Runs on Ubuntu latest
- Uses standard Python setup from actions/setup-python
- Tests against Python 3.9, 3.10, and 3.11
- Includes additional tests for pip installation methods

```mermaid
flowchart TD
    Linux["linux job"] --> Setup["Setup Python"]
    Setup --> InstallDeps["Install dependencies"]
    InstallDeps --> RunTests["Run tests"]
    RunTests --> TestPip["Test pip installation"]
    
    subgraph "Linux Requirements"
        CoreReqs["Core requirements"]
        TestReqs["Test requirements"]
        OptionalReqs["Optional requirements"]
        DocReqs["Documentation requirements"]
    end
    
    InstallDeps --> CoreReqs
    InstallDeps --> TestReqs
    InstallDeps --> OptionalReqs
    InstallDeps --> DocReqs
```

Sources: [.github/workflows/test_suite.yml:84-128]()

## 3. Test Suite Configuration

The test suite runs comprehensive tests across all platforms with specific configurations for each environment.

### 3.1 Testing Tools and Coverage

MoviePy uses pytest for testing, with additional configurations:
- Code coverage reporting with `--cov moviepy --cov-report term-missing`
- Doctests execution with `--doctest-glob "moviepy/**/**.py"`
- Verbose output with `-v` flag

```
python -m pytest --doctest-glob "moviepy/**/**.py" --cov moviepy --cov-report term-missing
```

Sources: [.github/workflows/test_suite.yml:54](), [.github/workflows/test_suite.yml:82](), [.github/workflows/test_suite.yml:121]()

## 4. Code Quality Checks

### 4.1 Pre-commit Hooks

MoviePy uses pre-commit hooks to enforce code style and quality standards before code is committed to the repository.

```mermaid
flowchart LR
    Developer["Developer"] -->|"git commit"| PreCommit["Pre-commit hooks"]
    
    subgraph "Pre-commit Hooks"
        Black["black formatter"]
        Isort["isort import sorter"]
        Flake8["flake8 linter"]
    end
    
    PreCommit --> Black
    PreCommit --> Isort
    PreCommit --> Flake8
    
    Black -->|"Pass/Fail"| Result["Commit result"]
    Isort -->|"Pass/Fail"| Result
    Flake8 -->|"Pass/Fail"| Result
```

The pre-commit configuration includes:
- **Black**: Code formatting with version 23.7.0
- **isort**: Import sorting with version 5.12.0
- **flake8**: Linting with version 6.0.0 and additional plugins:
  - flake8-absolute-import
  - flake8-docstrings
  - flake8-rst-docstrings
  - flake8-implicit-str-concat

Sources: [.pre-commit-config.yaml:1-26]()

### 4.2 CodeQL Analysis

GitHub's CodeQL analysis is configured to run automatically to detect security vulnerabilities:
- Triggered on pushes to the master branch
- Triggered on pull requests to the master branch
- Scheduled to run weekly (every Wednesday at 3:00 AM)

```mermaid
flowchart TD
    Push["Push to master"] --> Trigger["CodeQL Trigger"]
    PR["Pull Request"] --> Trigger
    Schedule["Weekly Schedule"] --> Trigger
    
    Trigger --> Init["Initialize CodeQL"]
    Init --> Build["Autobuild"]
    Build --> Analyze["Analyze Code"]
    
    subgraph "CodeQL Configuration"
        Language["Python language"]
        AutoDetect["Automatic language detection"]
        DefaultQueries["Default security queries"]
    end
    
    Init --> Language
    Init --> AutoDetect
    Analyze --> DefaultQueries
```

Sources: [.github/workflows/codeql-analysis.yml:1-57]()

## 5. Integration with MoviePy Development

The CI/CD workflow integrates into the larger MoviePy development process, ensuring that code contributions maintain quality standards and functionality.

### 5.1 Workflow for Contributors

```mermaid
flowchart TD
    Contributor["Contributor"] -->|"Fork repository"| Fork["MoviePy Fork"]
    Fork -->|"Clone locally"| LocalRepo["Local Repository"]
    LocalRepo -->|"Create branch"| Feature["Feature Branch"]
    Feature -->|"Make changes"| Changes["Code Changes"]
    Changes -->|"Run pre-commit"| PreCommit["Pre-commit Hooks"]
    PreCommit -->|"Commit changes"| Commit["Commit"]
    Commit -->|"Push to fork"| PushFork["Push to Fork"]
    PushFork -->|"Create PR"| PR["Pull Request"]
    
    PR -->|"Triggers"| CI["CI/CD Pipeline"]
    CI -->|"Run tests"| Tests["Test Suite"]
    CI -->|"Run CodeQL"| Security["Security Analysis"]
    
    Tests -->|"Pass/Fail"| Results["CI Results"]
    Security -->|"Pass/Fail"| Results
    
    Results -->|"Pass"| Review["Ready for Review"]
    Results -->|"Fail"| Fix["Fix Issues"]
    Fix --> Changes
```

Sources: [.github/workflows/test_suite.yml:1-129](), [.github/workflows/codeql-analysis.yml:1-57](), [.pre-commit-config.yaml:1-26]()

### 5.2 Relationship to Code Architecture

The CI/CD workflow tests all aspects of the MoviePy architecture, ensuring that changes don't break existing functionality.

```mermaid
flowchart TD
    CI["CI Pipeline"] --> TestCore["Test Core Components"]
    CI --> TestIO["Test I/O System"]
    CI --> TestFX["Test Effects"]
    
    subgraph "MoviePy Components Tested"
        CoreClasses["Clip, VideoClip, AudioClip Classes"]
        IOSystem["FFmpeg Integration"]
        EffectsSystem["Video/Audio Effects"]
    end
    
    TestCore --> CoreClasses
    TestIO --> IOSystem
    TestFX --> EffectsSystem
    
    CoreClasses --> TestExamples["Resize, Rotate, Crop, etc."]
    IOSystem --> FFmpegTests["ffmpeg_reader, ffmpeg_writer"]
    EffectsSystem --> EffectTests["Effects implementation"]
```

The CI pipeline specifically tests various MoviePy components including:

1. **Core Classes**: Testing functionality of `Clip`, `VideoClip`, and `AudioClip` classes
2. **Effects System**: Testing various effects like `Resize`, `Rotate`, `Crop`, etc.
3. **I/O System**: Testing FFmpeg integration for reading and writing media

Sources: [.github/workflows/test_suite.yml:50-54](), [moviepy/video/fx/Resize.py:1-158](), [moviepy/video/fx/Rotate.py:1-128](), [moviepy/video/fx/Crop.py:1-81](), [moviepy/video/io/ffplay_previewer.py:1-138]()

## 6. Maintenance and Updates

The CI/CD workflow itself is regularly maintained and updated to ensure compatibility with the latest tools and platforms. The configuration files include:

| File | Purpose |
|------|---------|
| `.github/workflows/test_suite.yml` | Main test workflow configuration |
| `.github/workflows/codeql-analysis.yml` | Security analysis configuration |
| `.pre-commit-config.yaml` | Pre-commit hooks configuration |
| `appveyor.yml` | Legacy CI configuration (minimal) |

Sources: [.github/workflows/test_suite.yml:1-129](), [.github/workflows/codeql-analysis.yml:1-57](), [.pre-commit-config.yaml:1-26](), [appveyor.yml:1-4]()

## Summary

MoviePy's CI/CD workflow ensures code quality and functionality through automated testing across multiple platforms and Python versions. The combination of GitHub Actions, pre-commit hooks, and CodeQL analysis provides a robust system for maintaining the codebase's integrity and security. Contributors should ensure their changes pass all tests and code quality checks before submitting pull requests.