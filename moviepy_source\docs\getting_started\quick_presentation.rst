.. _quick_presentation:

Quick presentation
===================

This section explains when MoviePy can be used and how it works.

Do I need MoviePy?
~~~~~~~~~~~~~~~~~~~

Here are a few reasons why you may want to edit videos in Python:

- You have many videos to process or to compose in a complicated way.
- You want to automate the creation of videos or GIFs on a web server (Django, Flask, etc.)
- You want to automate tedious tasks, like title insertions tracking objects, cutting scenes, making end credits, subtitles, etc...
- You want to code your own video effects to do something no existing video editor can.
- You want to create animations from images generated by another python library (Matplotlib, Mayavi, Gizeh, scikit-images...)

And here are a few uses for which MoviePy is NOT the best solution:

- You only need to do frame-by-frame video analysis (with face detection or other fancy stuff). This could be done with MoviePy in association with other libraries, but really, just use imageio_, OpenCV_ or SimpleCV, these are libraries that specialize in these tasks.
- You only want to convert a video file, or turn a series of image files into a movie. In this case it is better to directly call ``ffmpeg`` (or ``avconv`` or ``mencoder``...) as it will be faster and more memory-efficient than going through MoviePy.


Advantages and limitations
~~~~~~~~~~~~~~~~~~~~~~~~~~~

MoviePy has been developed with the following goals in mind:

- **Simple and intuitive**. Basic operations can be done in one line. The code is easy to learn and easy to understand for newcomers.
- **Flexible**. You have total control over the frames of the video and audio, and creating your own effects is easy as Py.
- **Portable**. The code uses very common software (Numpy and FFmpeg) and can run on (almost) any machine with (almost) any version of Python.

Limitations:
- MoviePy cannot stream videos (e.g. reading from a webcam, or rendering a video live on a distant machine).
- MoviePy is not really designed for video processing involving many successive frames of a movie (e.g. video stabilization - there is other software better suited for that).
- You can also have memory problems if you use many video, audio, and image sources at the same time (>100).

Example code
~~~~~~~~~~~~~~

In a typical MoviePy script, you load video or audio files, modify them, put them together, and write the final result to a new video file. As an example, let us load a video, lower the volume, add a title in the center of the video for the first ten seconds, and write the result in a file: 

.. literalinclude:: /_static/code/getting_started/quick_presentation/basic_example.py
    :language: python


How MoviePy works
~~~~~~~~~~~~~~~~~~~

MoviePy uses the software ``ffmpeg`` to read and to export video and audio files. It also (optionally) uses ``ffplay`` to allow for video previewing.

Internally, the representation and manipulation of the different media is done using Python's fast numerical library Numpy. Advanced effects and enhancements also use ``pillow`` library.

.. image:: /_static/medias/getting_started/explanations.jpeg
    :width: 570px
    :align: center


The central concept, the clips
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The central object of MoviePy is the the :py:class:`Clip <moviepy.Clip.Clip>`, with either :py:class:`~moviepy.audio.AudioClip.AudioClip` for any audio element, or :py:class:`~moviepy.video.VideoClip.VideoClip` for any visual element. Clips really are the base unit of MoviePy, everything you do is with and on them.

Clips can be created from more than just videos or audios though. They can also be created from an image, a text, a custom animation, a folder of images, and even a simple lambda function!

To create your final video, what you will do is essentially:   

#. Load different resources as clips (see :ref:`loading`)
#. Modify them (see :ref:`modifying`)
#. Mixing them into one final clip (see :ref:`compositing`)
#. Render them into a file (see :ref:`rendering`)

Of course, MoviePy offer multiple handy solution and tools to facilitate all these steps, and lets you add new ones by writing your own effects (see :ref:`create_effects`)!


.. _imageio: https://imageio.github.io/
.. _OpenCV: http://opencv.org/





